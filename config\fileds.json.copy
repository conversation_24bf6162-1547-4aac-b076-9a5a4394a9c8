{"马桶贴": [{"title": "货号", "excel_column": "货号"}, {"title": "商品轮播图", "excel_column": "商品轮播图", "type": "grid_wrapper", "require": 1}, {"title": "商品名称", "excel_column": "商品名称", "type": "textarea", "require": 1}, {"title": "安装类型", "excel_column": "安装类型", "type": "select_input", "require": 1}, {"title": "材料", "excel_column": "材料", "type": "select_input", "require": 1}, {"title": "点缀功能", "excel_column": "点缀功能", "type": "select_input", "require": 1}, {"title": "表面推荐", "excel_column": "表面推荐", "type": "select_input", "require": 1}, {"title": "可重用性", "excel_column": "可重用性", "type": "select_input", "require": 1}, {"title": "图案", "excel_column": "图案", "type": "select_input", "require": 1}, {"title": "形状", "excel_column": "形状", "type": "select_input", "require": 1}, {"title": "完成类型", "excel_column": "完成类型", "type": "select_input", "require": 1}, {"title": "父规格1", "excel_column": "属性名1", "type": "product_skc_select", "require": 1}, {"title": "商品规格1", "excel_column": "属性值1", "type": "product_skc_input", "require": 1}, {"title": "父规格2", "excel_column": "属性名2", "type": "product_skc_select"}, {"title": "商品规格2", "excel_column": "属性值2", "type": "product_skc_input"}, {"title": "父规格3", "excel_column": "属性名3", "type": "product_skc_select"}, {"title": "商品规格3", "excel_column": "属性值3", "type": "product_skc_input"}, {"title": "父规格4", "excel_column": "属性名4", "type": "product_skc_select"}, {"title": "商品规格4", "excel_column": "属性值4", "type": "product_skc_input"}, {"title": "父规格5", "excel_column": "属性名5", "type": "product_skc_select"}, {"title": "商品规格5", "excel_column": "属性值5", "type": "product_skc_input"}, {"title": "外包装图片", "type": "upload_input", "require": 1}, {"title": "外包装类型", "excel_column": "外包装类型", "type": "radio", "require": 1}, {"title": "外包装形状", "excel_column": "外包装形状", "type": "radio", "require": 1}, {"title": "敏感属性", "type": "dropdown_select", "require": 1, "value": "否"}, {"title": "最长边", "excel_column": "最长边(cm)", "type": "input_number", "require": 1}, {"title": "次长边", "excel_column": "次长边(cm)", "type": "input_number", "require": 1}, {"title": "最短边", "excel_column": "最短边(cm)", "type": "input_number", "require": 1}, {"title": "重量", "excel_column": "重量(g)", "type": "input_in_table", "require": 1}, {"title": "申报价格(", "excel_column": "申报价格(CNY)", "type": "input_in_table", "require": 1}, {"title": "预览图", "type": "sku_preview", "require": 1}, {"title": "SKU分类", "excel_column": "SKU分类", "type": "dropdown_select", "require": 1}, {"title": "建议零售价", "type": "input_in_table", "require": 1, "value": "NA"}, {"title": "SKU货号", "type": "input_in_table", "excel_column": "货号", "require": 1}, {"title": "详情图文", "type": "decoration", "require": 1}]}