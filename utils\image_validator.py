#!/usr/bin/env python3
"""
图片验证和重命名工具
"""

from pathlib import Path
from loguru import logger


def validate_and_rename_images(goods_path: Path, sku: str) -> None:
    """
    验证和重命名图片文件
    
    Args:
        goods_path: 商品文件夹路径
        sku: SKU号（货号）
        
    Raises:
        ValueError: 如果图片命名不符合规范
    """
    if not goods_path.exists() or not goods_path.is_dir():
        raise ValueError(f"商品文件夹不存在: {goods_path}")
    
    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    image_files = [f for f in goods_path.iterdir() 
                  if f.is_file() and f.suffix.lower() in image_extensions]
    
    if not image_files:
        raise ValueError(f"文件夹 {goods_path.name} 中没有找到图片文件")
    
    logger.info(f"验证文件夹 {sku} 中的 {len(image_files)} 张图片")
    
    # 分类现有图片
    valid_sku_files = []    # 已经是SKU-序号格式的文件
    numbered_files = []     # 1-5序号的文件
    invalid_files = []      # 其他不符合规范的文件
    
    for file_path in image_files:
        stem = file_path.stem
        
        # 检查是否已经是SKU-序号格式 (如: HZ-A138-1)
        if stem.startswith(f"{sku}-") and stem.split('-')[-1].isdigit():
            seq = int(stem.split('-')[-1])
            if 1 <= seq <= 5:
                valid_sku_files.append((seq, file_path))
                continue
        
        # 检查是否是1-5序号格式
        if stem in ['1', '2', '3', '4', '5']:
            numbered_files.append((int(stem), file_path))
            continue
            
        # 其他文件都是无效的
        invalid_files.append(file_path)
    
    # 如果有无效文件，报错
    if invalid_files:
        invalid_names = [f.name for f in invalid_files]
        raise ValueError(
            f"文件夹 {sku} 中存在命名不规范的图片文件: {invalid_names}\n"
            f"图片文件名必须是 1-5 的序号或 {sku}-1 到 {sku}-5 的格式"
        )
    
    # 重命名1-5序号的文件为SKU-序号格式
    for seq, file_path in numbered_files:
        new_name = f"{sku}-{seq}{file_path.suffix}"
        new_path = file_path.parent / new_name
        
        # 检查是否已存在同名的SKU格式文件
        if any(existing_seq == seq for existing_seq, _ in valid_sku_files):
            raise ValueError(
                f"文件夹 {sku} 中同时存在 {file_path.name} 和 {new_name}，请删除重复文件"
            )
        
        try:
            file_path.rename(new_path)
            logger.info(f"重命名图片: {file_path.name} -> {new_name}")
            valid_sku_files.append((seq, new_path))
        except Exception as e:
            raise ValueError(f"重命名图片失败: {file_path.name} -> {new_name}: {str(e)}")
    
    # 验证最终结果：必须有连续的1-5序号的图片
    final_sequences = sorted([seq for seq, _ in valid_sku_files])
    if not final_sequences:
        raise ValueError(f"文件夹 {sku} 中没有找到有效的图片文件")
    
    # 必须有5张图片，序号必须是1-5
    if len(final_sequences) != 5:
        raise ValueError(
            f"文件夹 {sku} 中图片数量不正确，当前有 {len(final_sequences)} 张图片，"
            f"必须有5张图片（序号1-5）"
        )
    
    if final_sequences != [1, 2, 3, 4, 5]:
        raise ValueError(
            f"文件夹 {sku} 中图片序号不正确，当前序号: {final_sequences}，"
            f"必须是连续的 1-5 序号"
        )
    
    logger.info(f"文件夹 {sku} 图片验证完成，共 5 张图片")
