#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块

提供统一的日志配置管理，支持通过环境变量控制日志级别
"""

import os
import sys
from loguru import logger


class LoggerConfig:
    """日志配置管理类"""
    
    # 默认日志级别
    DEFAULT_LEVEL = "INFO"
    
    # 支持的日志级别
    VALID_LEVELS = ["TRACE", "DEBUG", "INFO", "SUCCESS", "WARNING", "ERROR", "CRITICAL"]
    
    @classmethod
    def setup_logger(cls, level: str = None, enable_file_log: bool = True):
        """
        设置日志配置
        
        Args:
            level: 日志级别，如果为None则从环境变量读取
            enable_file_log: 是否启用文件日志
        """
        # 移除默认的日志处理器
        logger.remove()
        
        # 确定日志级别
        if level is None:
            level = cls.get_log_level_from_env()
        
        # 验证日志级别
        if level.upper() not in cls.VALID_LEVELS:
            level = cls.DEFAULT_LEVEL
        
        # 设置控制台日志格式
        console_format = cls._get_console_format(level.upper())
        
        # 添加控制台日志处理器
        logger.add(
            sys.stdout,
            format=console_format,
            level=level.upper(),
            colorize=True,
            backtrace=False,
            diagnose=False
        )
        
        # 添加文件日志处理器（如果启用）
        if enable_file_log:
            cls._setup_file_logger(level.upper())
        
        logger.info(f"日志系统已初始化，级别: {level.upper()}")
    
    @classmethod
    def get_log_level_from_env(cls) -> str:
        """
        从环境变量获取日志级别
        
        支持的环境变量：
        - LOG_LEVEL: 直接指定日志级别
        - DEBUG: 如果设置为1或true，则使用DEBUG级别
        
        Returns:
            str: 日志级别
        """
        # 优先检查 LOG_LEVEL 环境变量
        log_level = os.getenv("LOG_LEVEL", "").upper()
        if log_level in cls.VALID_LEVELS:
            return log_level
        
        # 检查 DEBUG 环境变量
        debug_env = os.getenv("DEBUG", "").lower()
        if debug_env in ["1", "true", "yes", "on"]:
            return "DEBUG"
        
        return cls.DEFAULT_LEVEL
    
    @classmethod
    def _get_console_format(cls, level: str) -> str:
        """
        根据日志级别获取控制台格式
        
        Args:
            level: 日志级别
            
        Returns:
            str: 日志格式字符串
        """
        if level == "DEBUG":
            # DEBUG模式显示更详细的信息
            return (
                "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                "<level>{message}</level>"
            )
        else:
            # 普通模式显示简洁信息
            return (
                "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{name}</cyan> - "
                "<level>{message}</level>"
            )
    
    @classmethod
    def _setup_file_logger(cls, level: str):
        """
        设置文件日志
        
        Args:
            level: 日志级别
        """
        try:
            # 创建logs目录
            logs_dir = "logs"
            os.makedirs(logs_dir, exist_ok=True)
            
            # 添加文件日志处理器
            logger.add(
                f"{logs_dir}/app.log",
                format=(
                    "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                    "{level: <8} | "
                    "{name}:{function}:{line} - "
                    "{message}"
                ),
                level=level,
                rotation="10 MB",
                retention="7 days",
                compression="zip",
                backtrace=True,
                diagnose=True
            )
        except Exception as e:
            logger.warning(f"无法设置文件日志: {e}")
    
    @classmethod
    def enable_debug(cls):
        """启用DEBUG日志级别"""
        cls.setup_logger("DEBUG")
    
    @classmethod
    def disable_debug(cls):
        """禁用DEBUG日志级别，使用INFO级别"""
        cls.setup_logger("INFO")
    
    @classmethod
    def set_level(cls, level: str):
        """
        设置日志级别
        
        Args:
            level: 日志级别
        """
        cls.setup_logger(level)


def setup_default_logger():
    """设置默认日志配置"""
    LoggerConfig.setup_logger()


def enable_debug_logging():
    """启用DEBUG日志"""
    LoggerConfig.enable_debug()


def disable_debug_logging():
    """禁用DEBUG日志"""
    LoggerConfig.disable_debug()


# 自动初始化日志配置
if __name__ != "__main__":
    setup_default_logger()
