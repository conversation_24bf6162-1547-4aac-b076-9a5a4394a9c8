# Temu 自动化工具 - 完整菜单系统使用说明

## 🎯 概述

全新的 Temu 自动化工具采用了完整的中文菜单系统，提供了导入用户、选择用户登录、上传商品、登出等完整功能。系统界面美观、操作简单、功能完整。

## ✨ 主要功能

### 🏠 主菜单界面

启动程序后，您将看到美观的主菜单界面：

```
🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟
🎯 欢迎使用 Temu 自动化工具
🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟
版本: v2.1.0 | 简化版菜单界面
🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟🌟

📊 当前状态:
  📋 用户数据: 3 个用户
  🔐 登录状态: 未登录

🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸
📋 主菜单
🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸
  [1] 📥 导入用户
  [2] 🔐 选择用户登录
  [3] 📦 上传商品 (需要先登录)
  [0] ❌ 退出程序
🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸
💡 请选择操作 (0-2):
```

### 📥 1. 导入用户功能

**功能说明**：从外部CSV文件导入用户数据到系统中。

**操作步骤**：
1. 在主菜单选择 `1` - 导入用户
2. 输入CSV文件的完整路径
3. 系统自动验证文件格式和内容
4. 成功导入后显示用户列表预览

**CSV文件格式**：
```csv
index,phone,password,username
1,13800138000,test123456,测试用户1
2,13800138001,test123456,测试用户2
3,13800138002,test123456,测试用户3
```

**字段说明**：
- `index`: 用户序号（必填，唯一标识）
- `phone`: 手机号（必填，用于登录）
- `password`: 密码（必填，用于登录）
- `username`: 用户名（必填，用于显示）

**安全特性**：
- 自动备份现有用户文件
- 文件格式验证
- 导入失败时自动恢复备份

### 🔐 2. 选择用户登录功能

**功能说明**：从已导入的用户中选择一个进行登录。

**操作步骤**：
1. 在主菜单选择 `2` - 选择用户登录
2. 系统显示美观的用户选择界面
3. 输入用户编号选择要登录的用户
4. 确认选择后系统自动执行登录
5. 登录成功后返回主菜单

**用户选择界面**：
```
🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹
🎯  Temu 账号选择器
🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹
📊 共找到 3 个可用账号

  [1] 测试用户1 (138****8000) - ✅ 可用
  [2] 测试用户2 (138****8001) - ✅ 可用
  [3] 测试用户3 (138****8002) - ✅ 可用

  [0] 退出程序
🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹
💡 请输入选项编号 (1-3, 0=退出):
```

**特色功能**：
- 彩色界面显示
- 手机号隐私保护（隐藏中间4位）
- 用户状态验证
- 确认选择机制

### 📦 3. 上传商品功能

**功能说明**：登录后可以上传商品数据到Temu平台。

**前置条件**：必须先完成用户登录

**操作步骤**：
1. 确保已登录用户
2. 在主菜单选择 `3` - 上传商品
3. 输入商品数据文件夹路径
4. 确认开始上传
5. 系统自动处理各个分类文件夹
6. 显示上传进度和结果

**登录后的菜单界面**：
```
📊 当前状态:
  📋 用户数据: 3 个用户
  🔐 登录状态: 已登录 - 测试用户1 (138****8000)

🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸
📋 主菜单
🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸
  [1] 📥 导入用户
  [2] 🔐 选择用户登录
  [3] 📦 上传商品
  [4] 🚪 登出
  [0] ❌ 退出程序
🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸🔸
💡 请选择操作 (0-4):
```

### 🚪 4. 登出功能

**功能说明**：安全登出当前用户。

**操作步骤**：
1. 在主菜单选择 `4` - 登出
2. 确认登出操作
3. 系统清除登录状态
4. 返回未登录状态的主菜单

### ❌ 0. 退出程序

**功能说明**：安全退出程序。

**操作步骤**：
1. 在主菜单选择 `0` - 退出程序
2. 如果当前有用户登录，询问是否先登出
3. 显示感谢信息并退出

## 🚀 使用方法

### 启动程序

```bash
# 激活虚拟环境
source .venv/bin/activate

# 启动程序（无命令行参数时自动进入菜单模式）
python index.py
```

### 完整操作流程示例

1. **首次使用 - 导入用户**：
   ```
   选择 1 → 输入CSV文件路径 → 确认导入
   ```

2. **登录用户**：
   ```
   选择 2 → 选择用户编号 → 确认登录
   ```

3. **上传商品**：
   ```
   选择 3 → 输入商品数据路径 → 确认上传
   ```

4. **安全退出**：
   ```
   选择 0 → 确认登出 → 退出程序
   ```

## 🛡️ 安全特性

### 数据保护
- 自动备份用户文件
- 手机号隐私保护
- 密码安全存储

### 错误处理
- 完善的异常处理机制
- 友好的错误提示
- 自动恢复功能

### 状态管理
- 实时显示当前状态
- 登录状态验证
- 操作权限控制

## 🎨 界面特色

### 美观设计
- 彩色界面显示
- 图标和表情符号
- 清晰的布局结构

### 用户体验
- 直观的操作流程
- 友好的提示信息
- 确认机制防误操作

### 响应式设计
- 适配不同终端
- 优雅的错误处理
- 流畅的交互体验

## 🔧 故障排除

### 常见问题

1. **用户文件导入失败**
   - 检查CSV文件格式是否正确
   - 确认文件路径是否存在
   - 验证文件编码为UTF-8

2. **登录失败**
   - 检查手机号和密码是否正确
   - 确认网络连接正常
   - 验证浏览器是否正常工作

3. **上传商品失败**
   - 确保已成功登录用户
   - 检查商品数据目录结构
   - 验证配置文件是否正确

### 技术支持

如遇到问题，请提供：
- 错误信息截图
- 操作步骤描述
- 系统环境信息

## 📝 更新日志

### v2.1.0 (当前版本)
- ✨ 全新的中文菜单系统
- 📥 用户导入功能
- 🔐 美观的用户选择界面
- 📦 集成的商品上传功能
- 🚪 安全的登出机制
- 🛡️ 完善的错误处理
- 🎨 彩色界面设计

## 🎉 总结

新版本的 Temu 自动化工具提供了完整的中文菜单系统，操作简单、功能完整、界面美观。无论是新用户还是老用户，都能快速上手并高效完成工作。

**主要优势**：
- 🎯 **操作简单**：直观的菜单导航
- 🔒 **安全可靠**：完善的数据保护
- 🎨 **界面美观**：彩色设计和图标
- 🚀 **功能完整**：涵盖所有核心功能
- 🛠️ **易于维护**：模块化设计架构

立即开始使用，体验全新的 Temu 自动化工具！
