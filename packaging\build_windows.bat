@echo off
chcp 65001 >nul
echo 🎯 Temu 自动化工具 Windows 打包脚本
echo ==========================================

echo.
echo 📋 检查 Python 环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python 未安装或未添加到 PATH
    pause
    exit /b 1
)

echo.
echo 📦 检查虚拟环境...
if not exist "venv" (
    echo 🔧 创建虚拟环境...
    python -m venv venv
)

echo.
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 📥 安装 Windows 依赖包...
pip install -r packaging/requirements-windows.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 🧹 清理之前的构建文件...
if exist "build" rmdir /s /q build
if exist "dist" rmdir /s /q dist
if exist "__pycache__" rmdir /s /q __pycache__

echo.
echo 🔨 开始打包...
pyinstaller packaging/temu_app.spec --clean --noconfirm
if %errorlevel% neq 0 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 📁 创建发布目录...
if not exist "release" mkdir release

echo.
echo 📋 复制必要文件到发布目录...
copy "dist\TemuAutoTool.exe" "release\"
if not exist "release\data" mkdir "release\data"
copy "data\users.csv.copy" "release\data\"
if not exist "release\config" mkdir "release\config"
copy "config\*" "release\config\"

echo.
echo 📝 创建使用说明...
echo 🎯 Temu 自动化工具 Windows 版 > "release\使用说明.txt"
echo. >> "release\使用说明.txt"
echo 📋 使用方法: >> "release\使用说明.txt"
echo 1. 双击 TemuAutoTool.exe 启动程序 >> "release\使用说明.txt"
echo 2. 首次使用请先导入用户CSV文件 >> "release\使用说明.txt"
echo 3. 用户CSV文件格式参考 data\users.csv.copy >> "release\使用说明.txt"
echo. >> "release\使用说明.txt"
echo 📁 文件说明: >> "release\使用说明.txt"
echo - TemuAutoTool.exe: 主程序 >> "release\使用说明.txt"
echo - data\users.csv.copy: 用户数据模板 >> "release\使用说明.txt"
echo - config\: 配置文件目录 >> "release\使用说明.txt"
echo. >> "release\使用说明.txt"
echo 🔧 技术支持: >> "release\使用说明.txt"
echo 如遇问题请联系开发者 >> "release\使用说明.txt"

echo.
echo ✅ 打包完成！
echo 📁 发布文件位于: release\
echo 📋 主程序: release\TemuAutoTool.exe
echo.
echo 🧪 测试打包结果...
cd release
TemuAutoTool.exe --help
if %errorlevel% neq 0 (
    echo ⚠️  程序可能存在问题，请检查
) else (
    echo ✅ 程序运行正常
)
cd ..

echo.
echo 🎉 打包流程完成！
echo 📦 可执行文件: release\TemuAutoTool.exe
echo 📋 使用说明: release\使用说明.txt
echo.
pause
