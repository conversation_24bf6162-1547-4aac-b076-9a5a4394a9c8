🎯 Temu自动化工具 V1.0.0 Windows版

⚠️ 运行前必需软件安装
==================

本程序需要以下软件支持，请确保已正确安装：

1. 🌐 Chrome浏览器（必需）
   - 下载地址：https://www.google.com/intl/zh-CN/chrome/
   - 说明：用于网页自动化操作，必须安装最新版本

📋 快速开始
==================

1. 📥 安装必需软件
   - 下载并安装Chrome浏览器
   - 确保Chrome为最新版本

2. 🚀 启动程序
   - 双击 TemuAutoTool.exe 启动程序
   - 首次启动可能需要5-10秒加载时间

3. 📊 导入用户数据
   - 选择菜单 [1] 导入用户
   - 准备CSV格式的用户数据文件
   - 参考 data/users.csv 模板格式

4. 🔐 登录账户
   - 选择菜单 [2] 选择用户登录
   - 从用户列表中选择要使用的账户

5. 📦 上传商品
   - 选择菜单 [3] 上传商品
   - 选择包含商品数据的文件夹路径

📁 商品目录格式要求
==================

商品数据文件夹结构示例：
D:\temu\goods\
├── 马桶贴\                    ← 商品分类目录（对应Temu中的分类）
│   ├── goods_toilet_stickers.xlsx  ← 商品数据Excel文件
│   ├── HZ-M090-1.jpg              ← 商品图片（货号-序号.jpg）
│   ├── HZ-M090-2.png              ← 支持多种格式
│   └── ...
├── 圆柱体包装\                ← 固定包装目录
│   ├── 1.jpg                     ← 包装图片（序号.jpg）
│   ├── 2.png
│   └── ...
└── 长方体包装\                ← 固定包装目录
    ├── 1.jpg                     ← 包装图片（序号.jpg）
    ├── 2.png
    └── ...

重要说明：
- 商品分类目录名必须与Temu中的分类名称一致
- 圆柱体包装、长方体包装是固定目录名，不可更改
- 商品图片命名格式：货号-序号.扩展名（如：HZ-M090-1.jpg）
- 包装图片命名格式：序号.扩展名（如：1.jpg, 2.png）

📁 文件说明
==================

- TemuAutoTool.exe: 主程序（包含所有依赖）
- data/users.csv: 用户数据模板文件
- config/fileds.json: 字段配置文件
- logs/: 日志文件目录（自动创建）

🔧 系统要求
==================

最低要求：
- 操作系统: Windows 10 (64位) 或更高版本
- 内存: 4GB RAM (推荐 8GB)
- 存储: 500MB 可用磁盘空间
- 网络: 稳定的互联网连接
- Chrome浏览器: 最新版本（必需）

❓ 常见问题
==================

Q: 程序无法启动？
A: 1. 确认系统为64位Windows
   2. 安装Chrome浏览器
   3. 临时关闭防病毒软件测试

Q: Chrome相关错误？
A: 1. 确认已安装Chrome浏览器
   2. 更新Chrome到最新版本
   3. 关闭其他Chrome窗口

� 技术支持
==================

如遇问题请：
1. 查看 logs/ 目录下的日志文件
2. 确认Chrome浏览器正常工作
3. 检查商品目录结构是否正确
4. 确认网络连接状态

版本：V1.0.0
更新日期：2025年7月
