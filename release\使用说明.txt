🎯 Temu 自动化工具 V1.0.0 Windows 版

⚠️ 运行前必需软件安装
==================

本程序需要以下软件支持，请确保已正确安装：

1. 🌐 Chrome浏览器（必需）
   - 下载地址：https://www.google.com/intl/zh-CN/chrome/
   - 说明：用于网页自动化操作，必须安装最新版本

2. 🐍 Python 3.12.10（开发环境需要，exe版本可选）
   - 下载地址：https://www.python.org/downloads/windows/
   - 直接下载：https://www.python.org/ftp/python/3.12.10/python-3.12.10-amd64.exe
   - 说明：如果只使用exe版本可不安装，如需源码运行则必需

📋 快速开始
==================

1. 📥 安装必需软件
   - 下载并安装Chrome浏览器
   - 确保Chrome为最新版本

2. 🚀 启动程序
   - 双击 TemuAutoTool.exe 启动程序
   - 首次启动可能需要5-10秒加载时间

3. 📊 导入用户数据
   - 选择菜单 [1] 导入用户
   - 准备CSV格式的用户数据文件
   - 参考 data/users.csv.copy 模板格式

4. 🔐 登录账户
   - 选择菜单 [2] 选择用户登录
   - 从用户列表中选择要使用的账户

5. 📦 上传商品
   - 选择菜单 [3] 上传商品
   - 选择包含商品数据的文件夹路径

📁 文件说明
==================

- TemuAutoTool.exe: 主程序（包含所有依赖）
- data/users.csv.copy: 用户数据模板文件
- config/fileds.json: 字段配置文件
- logs/: 日志文件目录（自动创建）
- docs/: 详细文档目录

🔧 系统要求
==================

最低要求：
- 操作系统: Windows 10 (64位) 或更高版本
- 内存: 4GB RAM (推荐 8GB)
- 存储: 500MB 可用磁盘空间
- 网络: 稳定的互联网连接

推荐配置：
- 操作系统: Windows 11
- 内存: 8GB RAM 或更高
- Chrome浏览器: 最新版本

❓ 常见问题
==================

Q: 程序无法启动？
A: 1. 确认系统为64位Windows
   2. 安装 Visual C++ 运行库
   3. 临时关闭防病毒软件测试

Q: Chrome相关错误？
A: 1. 确认已安装Chrome浏览器
   2. 更新Chrome到最新版本
   3. 关闭其他Chrome窗口

Q: 详情页装修未打开？
A: 1. 检查网络连接稳定性
   2. 等待页面完全加载
   3. 程序会自动重试3次

📞 技术支持
==================

如遇问题请：
1. 查看 logs/ 目录下的日志文件
2. 参考 docs/ 目录下的详细文档
3. 确认Chrome浏览器正常工作
4. 检查网络连接状态

版本：V1.0.0
更新日期：2025年7月
