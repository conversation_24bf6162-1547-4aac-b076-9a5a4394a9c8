#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
素材中心组件使用示例

展示如何使用素材中心组件和图片网格组件进行图片上传
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from loguru import logger
from components.material_center import MaterialCenterComponent
from components.grid_wrapper import GridWrapperComponent
from utils.chrome import get_page_by_id


def setup_test_images():
    """创建测试图片目录和文件"""
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)

    # 创建一些测试图片文件（空文件用于演示）
    test_files = [
        "HZ-A138-1.jpg",
        "HZ-A138-2.jpg",
        "HZ-A138-3.jpg",
        "HZ-A138-4.jpg",
        "HZ-A138-5.jpg"
    ]

    image_paths = []
    for file_name in test_files:
        test_file = test_dir / file_name
        if not test_file.exists():
            test_file.touch()
            logger.info(f"创建测试文件: {test_file}")
        image_paths.append(str(test_file))

    return image_paths  # 返回图片路径列表而不是目录路径


def example_material_center_standalone():
    """素材中心组件独立使用示例"""
    print("\n=== 素材中心组件独立使用示例 ===")
    
    try:
        # 获取浏览器页面
        page = get_page_by_id("example")
        tab = page.latest_tab
        
        # 打开测试页面（需要替换为实际的商品编辑页面）
        test_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab.get(test_url)
        tab.wait(3)
        
        # 创建素材中心组件
        material_center = MaterialCenterComponent(tab)
        
        # 准备测试图片（获取图片路径列表）
        test_image_paths = setup_test_images()

        print(f"准备上传 {len(test_image_paths)} 张图片:")
        for i, path in enumerate(test_image_paths, 1):
            print(f"  {i}. {os.path.basename(path)}")

        # 1. 打开素材中心
        print("\n1. 打开素材中心...")
        if result.is_success():
            print(f"✅ {result.message}")
        else:
            print(f"❌ {result.message}")
            return

        # 2. 批量上传图片（推荐方式）
        print("2. 批量上传图片...")
        result = material_center.upload_images(test_image_paths)
        if result.is_success():
            print(f"✅ {result.message}")
            if result.data:
                print(f"   上传详情: {result.data}")
        else:
            print(f"❌ {result.message}")
            return
        
        # 3. 选择图片
        print("3. 选择图片...")
        result = material_center.select_images(5)
        if result.is_success():
            print(f"✅ {result.message}")
            if result.data:
                print(f"   选择详情: {result.data}")
        else:
            print(f"❌ {result.message}")
            return
        
        # 4. 确认选择
        print("4. 确认选择...")
        result = material_center.confirm_selection()
        if result.is_success():
            print(f"✅ {result.message}")
        else:
            print(f"❌ {result.message}")
        
        print("✨ 素材中心独立使用示例完成")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")


def example_grid_wrapper_with_material_center():
    """图片网格组件集成素材中心使用示例"""
    print("\n=== 图片网格组件集成使用示例 ===")
    
    try:
        # 获取浏览器页面
        page = get_page_by_id("example")
        tab = page.latest_tab
        
        # 打开测试页面
        test_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab.get(test_url)
        tab.wait(3)
        
        # 创建图片网格组件（内部会自动创建素材中心组件）
        grid_wrapper = GridWrapperComponent(tab)
        
        # 准备测试数据（图片路径列表）
        test_image_paths = setup_test_images()
        field_data = {
            "value": test_image_paths,  # 传入图片路径列表
            "require": 1,
            "type": "grid_wrapper"
        }
        
        # 使用图片网格组件填充商品轮播图字段
        print("填充商品轮播图字段...")
        result = grid_wrapper.fill_data("商品轮播图", field_data)
        
        if result.is_success():
            print(f"✅ {result.message}")
        else:
            print(f"❌ {result.message}")
        
        print("✨ 图片网格组件集成使用示例完成")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")


def example_complete_upload_flow():
    """完整上传流程示例"""
    print("\n=== 完整上传流程示例 ===")
    
    try:
        # 获取浏览器页面
        page = get_page_by_id("example")
        tab = page.latest_tab
        
        # 打开测试页面
        test_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab.get(test_url)
        tab.wait(3)
        
        # 创建素材中心组件
        material_center = MaterialCenterComponent(tab)
        
        # 准备测试图片（图片路径列表）
        test_image_paths = setup_test_images()

        print(f"准备使用完整流程上传 {len(test_image_paths)} 张图片")

        # 使用完整流程方法
        print("执行完整上传流程...")
        result = material_center.complete_upload_flow(
            image_paths=test_image_paths,
            select_count=5
        )
        
        if result.is_success():
            print(f"✅ {result.message}")
            if result.data:
                print(f"   流程详情: {result.data}")
        else:
            print(f"❌ {result.message}")
        
        print("✨ 完整上传流程示例完成")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")


def example_upload_comparison():
    """批量上传 vs 逐个上传对比示例"""
    print("\n=== 批量上传 vs 逐个上传对比示例 ===")

    try:
        # 获取浏览器页面
        page = get_page_by_id("example")
        tab = page.latest_tab

        # 打开测试页面
        test_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab.get(test_url)
        tab.wait(3)

        # 创建素材中心组件
        material_center = MaterialCenterComponent(tab)

        # 准备测试图片
        test_image_paths = setup_test_images()

        print(f"准备对比两种上传方式，图片数量: {len(test_image_paths)}")

        # 方式1: 批量上传（推荐）
        print("\n🚀 方式1: 批量上传（推荐）")
        print("-" * 30)

        # 打开素材中心
        result = material_center.open_material_center()
        if result.is_success():
            print(f"✅ 打开素材中心成功")

            # 批量上传
            import time
            start_time = time.time()
            result = material_center.upload_images(test_image_paths)
            batch_time = time.time() - start_time

            if result.is_success():
                print(f"✅ 批量上传成功 (耗时: {batch_time:.2f}秒)")
                print(f"   {result.message}")
            else:
                print(f"❌ 批量上传失败: {result.message}")

            # 关闭素材中心
            material_center.close_material_center()
        else:
            print(f"❌ 打开素材中心失败: {result.message}")
            return

        time.sleep(2)

        # 方式2: 逐个上传（备用方案）
        print("\n🔄 方式2: 逐个上传（备用方案）")
        print("-" * 30)

        # 重新打开素材中心
        result = material_center.open_material_center()
        if result.is_success():
            print(f"✅ 重新打开素材中心成功")

            # 逐个上传
            start_time = time.time()
            result = material_center.upload_images_individually(test_image_paths)
            individual_time = time.time() - start_time

            if result.is_success():
                print(f"✅ 逐个上传成功 (耗时: {individual_time:.2f}秒)")
                print(f"   {result.message}")
            else:
                print(f"❌ 逐个上传失败: {result.message}")

            # 关闭素材中心
            material_center.close_material_center()
        else:
            print(f"❌ 重新打开素材中心失败: {result.message}")
            return

        # 性能对比
        print(f"\n📊 性能对比")
        print("-" * 20)
        if 'batch_time' in locals() and 'individual_time' in locals():
            speedup = individual_time / batch_time if batch_time > 0 else 1
            print(f"批量上传: {batch_time:.2f} 秒")
            print(f"逐个上传: {individual_time:.2f} 秒")
            print(f"性能提升: {speedup:.2f}x")

        print("✨ 上传方式对比示例完成")

    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")


def example_batch_upload():
    """批量上传示例"""
    print("\n=== 批量上传示例 ===")

    try:
        # 获取浏览器页面
        page = get_page_by_id("example")
        tab = page.latest_tab
        
        # 打开测试页面
        test_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab.get(test_url)
        tab.wait(3)
        
        # 创建图片网格组件
        grid_wrapper = GridWrapperComponent(tab)
        
        # 准备多个字段的测试数据（图片路径列表）
        test_image_paths = setup_test_images()

        fields_to_fill = [
            {
                "title": "商品轮播图",
                "data": {
                    "value": test_image_paths,  # 传入图片路径列表
                    "require": 1,
                    "type": "grid_wrapper"
                }
            },
            # 可以添加更多字段
            # {
            #     "title": "商品详情图",
            #     "data": {
            #         "value": test_images_path,
            #         "require": 0,
            #         "type": "grid_wrapper"
            #     }
            # }
        ]
        
        # 批量填充字段
        success_count = 0
        for field_info in fields_to_fill:
            field_title = field_info["title"]
            field_data = field_info["data"]
            
            print(f"填充字段: {field_title}")
            result = grid_wrapper.fill_data(field_title, field_data)
            
            if result.is_success():
                print(f"  ✅ {result.message}")
                success_count += 1
            else:
                print(f"  ❌ {result.message}")
        
        print(f"✨ 批量上传完成: {success_count}/{len(fields_to_fill)} 个字段成功")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")


def main():
    """主函数"""
    print("🚀 素材中心组件使用示例")
    print("=" * 50)
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    print("请选择要运行的示例:")
    print("1. 素材中心组件独立使用")
    print("2. 图片网格组件集成使用")
    print("3. 完整上传流程")
    print("4. 批量上传 vs 逐个上传对比")
    print("5. 批量上传示例")
    print("6. 运行所有示例")
    
    try:
        choice = input("\n请输入选择 (1-6): ").strip()

        if choice == "1":
            example_material_center_standalone()
        elif choice == "2":
            example_grid_wrapper_with_material_center()
        elif choice == "3":
            example_complete_upload_flow()
        elif choice == "4":
            example_upload_comparison()
        elif choice == "5":
            example_batch_upload()
        elif choice == "6":
            example_material_center_standalone()
            example_grid_wrapper_with_material_center()
            example_complete_upload_flow()
            example_upload_comparison()
            example_batch_upload()
        else:
            print("无效选择")
            return
        
        print("\n🎉 示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 运行失败: {str(e)}")


if __name__ == "__main__":
    main()
