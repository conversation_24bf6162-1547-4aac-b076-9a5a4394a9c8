#!/bin/bash
# -*- coding: utf-8 -*-
# Temu 自动化工具 macOS 打包脚本

set -e  # 遇到错误立即退出

echo "🎯 Temu 自动化工具 macOS 打包脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "❌ $1 未安装或未添加到 PATH"
        exit 1
    fi
}

echo
print_info "📋 检查系统环境..."
echo "🖥️  操作系统: $(uname -s) $(uname -r)"
echo "🏗️  架构: $(uname -m)"

echo
print_info "📋 检查 Python 环境..."
check_command python3
PYTHON_VERSION=$(python3 --version)
print_success "✅ $PYTHON_VERSION"

echo
print_info "📦 检查虚拟环境..."
if [ ! -d ".venv" ]; then
    print_info "🔧 创建虚拟环境..."
    python3 -m venv .venv
fi

echo
print_info "🔄 激活虚拟环境..."
source .venv/bin/activate

echo
print_info "📥 安装依赖包..."
pip install --upgrade pip

# 安装项目依赖
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
else
    print_warning "⚠️  requirements.txt 不存在"
fi

# 安装PyInstaller
pip install pyinstaller

echo
print_info "🧹 清理之前的构建文件..."
rm -rf build dist __pycache__ release
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

echo
print_info "🔨 开始打包..."
pyinstaller packaging/temu_app.spec --clean --noconfirm

if [ $? -ne 0 ]; then
    print_error "❌ 打包失败"
    exit 1
fi

echo
print_info "📁 创建发布目录..."
mkdir -p release

echo
print_info "📋 复制必要文件到发布目录..."

# 复制可执行文件
if [ -f "dist/TemuAutoTool" ]; then
    cp "dist/TemuAutoTool" "release/"
    chmod +x "release/TemuAutoTool"
    print_success "📋 复制可执行文件: TemuAutoTool"
else
    print_error "❌ 找不到可执行文件"
    exit 1
fi

# 复制数据文件
mkdir -p "release/data"
if [ -f "data/users.csv.copy" ]; then
    cp "data/users.csv.copy" "release/data/"
fi

# 复制配置文件
mkdir -p "release/config"
if [ -d "config" ]; then
    cp config/*.json "release/config/" 2>/dev/null || true
fi

echo
print_info "📝 创建使用说明..."
cat > "release/使用说明.txt" << EOF
🎯 Temu 自动化工具 macOS 版

📋 使用方法:
1. 在终端中运行: ./TemuAutoTool
2. 或者双击 TemuAutoTool 启动程序
3. 首次使用请先导入用户CSV文件
4. 用户CSV文件格式参考 data/users.csv.copy

📁 文件说明:
- TemuAutoTool: 主程序
- data/users.csv.copy: 用户数据模板
- config/: 配置文件目录

🔧 系统要求:
- macOS 10.14 或更高版本
- 架构: $(uname -m)

💡 使用提示:
- 如果提示"无法打开，因为无法验证开发者"，请在系统偏好设置 > 安全性与隐私中允许运行
- 或者在终端中运行: sudo xattr -rd com.apple.quarantine TemuAutoTool

🔧 技术支持:
如遇问题请联系开发者
EOF

echo
print_success "✅ 打包完成！"
print_info "📁 发布文件位于: release/"
print_info "📋 主程序: release/TemuAutoTool"

echo
print_info "🧪 测试打包结果..."
cd release

# 测试程序
if ./TemuAutoTool --help > /dev/null 2>&1; then
    print_success "✅ 程序运行正常"
else
    print_warning "⚠️  程序可能存在问题，请检查"
fi

cd ..

echo
print_success "🎉 打包流程完成！"
print_info "📦 可执行文件: release/TemuAutoTool"
print_info "📋 使用说明: release/使用说明.txt"

echo
print_info "💡 运行方式:"
print_info "   方式1: cd release && ./TemuAutoTool"
print_info "   方式2: 双击 release/TemuAutoTool"

echo
