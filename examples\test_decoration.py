"""
详情图文装饰组件测试

测试装饰组件的功能：
1. 按照图片名称index排序
2. 依次添加装饰图片
3. 验证图片上传和选择功能

先填充ProductSKC数据，然后测试装饰组件功能
"""

import sys
import os
import tempfile
from pathlib import Path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from biz.temu import Temu
from components.product_skc import ProductSkcComponent
from components import ComponentFactory


def create_test_images():
    """创建测试图片文件"""

    temp_dir = Path("/Users/<USER>/Desktop/temu/马桶/HZ-A138")
    test_images = [
        "HZ-A138-3.jpg",
        "HZ-A138-1.jpg",
        "HZ-A138-5.jpg",
        "HZ-A138-2.jpg",
        "HZ-A138-4.jpg",
    ]

    created_files = []
    for image_name in test_images:
        image_path = temp_dir / image_name
        # 创建空文件模拟图片
        created_files.append(str(image_path))

    print(f"📁 创建测试图片目录: {temp_dir}")
    print(f"📷 创建 {len(created_files)} 个测试图片文件")

    return str(temp_dir), created_files


def fill_product_skc_data(tab):
    """填充ProductSKC数据"""
    
    print("\n🔧 创建ProductSkcComponent实例...")
    product_skc_component = ProductSkcComponent(tab)
    
    # ProductSKC测试数据
    test_cases = [
        ("父规格1", {"value": "颜色", "require": 0}),
        ("商品规格1", {"value": "红色", "require": 0}),
    ]
    
    print("\n📝 开始填充ProductSKC数据...")
    for field_title, data in test_cases:
        print(f"\n🎯 填充: {field_title} = {data['value']}")
        
        result = product_skc_component.fill_data(field_title, data)
        
        if result.success:
            print(f"✅ {field_title} 成功: {result.message}")
        else:
            print(f"❌ {field_title} 失败: {result.message}")
        
        tab.wait(3)
    
    print(f"\n🎉 ProductSKC数据填充完成！")
    
    # 等待SKC数据处理完成，让页面更新
    print("⏳ 等待SKC数据处理完成...")
    tab.wait(5)
    
    # 点击页面空白区域确保数据保存
    print("💾 确保SKC数据保存...")
    try:
        tab.run_js("document.body.click();")
        tab.wait(3)
    except Exception as e:
        print(f"点击页面异常: {e}")
    
    print("✅ SKC数据处理完成")
    return True

#
# def test_image_sorting():
#     """测试图片排序功能"""
#
#     print("🎯 测试图片排序功能")
#     print("=" * 50)
#
#     try:
#         # 创建测试图片
#         temp_dir, created_files = create_test_images()
#
#         # 创建装饰组件实例
#         from components.decoration import DecorationComponent
#
#         # 创建一个虚拟的tab对象用于测试
#         class MockTab:
#             pass
#
#         decoration_component = DecorationComponent(MockTab())
#
#         # 测试图片排序
#         print(f"\n🔍 测试图片排序...")
#         print(f"📁 测试目录: {temp_dir}")
#
#         # 获取排序后的图片列表
#         sorted_images = decoration_component._get_decoration_images(temp_dir)
#
#         print(f"\n📋 排序结果 ({len(sorted_images)} 张图片):")
#         for i, image_path in enumerate(sorted_images, 1):
#             filename = os.path.basename(image_path)
#             print(f"   {i}. {filename}")
#
#         # 验证排序是否正确
#         expected_order = [
#             "decoration_1.jpg",
#             "decoration_2.jpg",
#             "decoration_3.jpg",
#             "decoration_4.jpg",
#             "decoration_5.jpg",
#             "image-1.png",
#             "image-2.png",
#             "image-10.png",
#             "pic_001.jpg",
#             "pic_002.jpg",
#             "pic_003.jpg"
#         ]
#
#         actual_order = [os.path.basename(path) for path in sorted_images]
#
#         print(f"\n🧪 验证排序结果...")
#         success_count = 0
#         for i, (expected, actual) in enumerate(zip(expected_order, actual_order)):
#             if expected == actual:
#                 print(f"   ✅ 位置 {i+1}: {actual}")
#                 success_count += 1
#             else:
#                 print(f"   ❌ 位置 {i+1}: 期望 {expected}, 实际 {actual}")
#
#         print(f"\n📊 排序测试结果: {success_count}/{len(expected_order)} 正确")
#
#         # 清理测试文件
#         import shutil
#         shutil.rmtree(temp_dir)
#         print(f"🗑️ 清理测试目录: {temp_dir}")
#
#         return success_count == len(expected_order)
#
#     except Exception as e:
#         print(f"❌ 图片排序测试失败: {str(e)}")
#         logger.exception("排序测试异常")
#         return False


def test_decoration_component():
    """测试装饰组件完整功能"""
    
    print("🎯 测试装饰组件完整功能")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 先填充ProductSKC数据
        if not fill_product_skc_data(tab):
            print("❌ ProductSKC数据填充失败")
            return False
        
        # # 4. 创建测试图片
        # temp_dir, created_files = create_test_images()
        temp_dir = "/Users/<USER>/Desktop/temu/马桶贴/HZ-A138"
        # 5. 测试装饰组件
        print(f"\n📝 开始测试装饰组件...")
        
        # 创建装饰组件
        decoration_component = ComponentFactory.create_component("decoration", tab)
        if not decoration_component:
            print("❌ 无法创建decoration组件")
            return False
        
        # 测试数据
        test_data = {
            "title": "详情图文",
            "type": "decoration",
            "require": 1,
            "value": temp_dir  # 使用测试图片目录
        }
        
        field_title = test_data["title"]
        field_data = {
            "value": test_data["value"],
            "require": test_data["require"]
        }

        print(f"\n🎯 测试字段: {field_title}")
        print(f"📋 测试数据: {field_data}")
        print(f"📁 图片目录: {temp_dir}")

        # 执行填充操作
        result = decoration_component.fill_data(field_title, field_data)

        if result.success:
            print(f"✅ {field_title} 成功: {result.message}")
        else:
            print(f"❌ {field_title} 失败: {result.message}")


        print(f"\n🎉 装饰组件测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False


def test_find_decoration_elements():
    """测试查找装饰相关元素"""
    
    print("🎯 测试查找装饰相关元素")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 先填充ProductSKC数据
        if not fill_product_skc_data(tab):
            print("❌ ProductSKC数据填充失败")
            return False
        
        # 4. 测试查找装饰元素
        print("\n🔍 测试查找装饰元素...")
        
        # 创建装饰组件
        decoration_component = ComponentFactory.create_component("decoration", tab)
        
        field_title = "详情图文装饰"
        print(f"\n🎯 查找字段: {field_title}")
        
        # 查找字段容器
        container = decoration_component.find_field_container(field_title)
        
        if container:
            print(f"✅ 成功找到 {field_title} 容器")
            print(f"📋 容器标签: {container.tag}")
            print(f"📋 容器文本: {container.text}")
            print(f"📋 容器属性: {container.attrs}")
        else:
            print(f"❌ 未找到 {field_title} 容器")
            
            # 尝试查找所有包含"装修"的元素
            print("\n🔍 查找所有包含'装修'的元素...")
            decoration_elements = tab.eles("x://*[contains(text(), '装修')]")
            print(f"📋 找到 {len(decoration_elements)} 个包含'装修'的元素")
            
            for i, element in enumerate(decoration_elements[:5]):  # 只显示前5个
                print(f"   元素 {i+1}: {element.tag} - {element.text[:50]}...")
                print(f"   属性: {element.attrs}")
        
        print(f"\n🎉 装饰元素查找测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False

def _get_decoration_images(self, value):
    """
    获取装饰图片文件路径列表，按照图片名称中的index排序

    Args:
        value: 装饰图片路径值

    Returns:
        按index排序的有效装饰图片文件路径列表
    """
    image_paths = []
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

    if isinstance(value, str):
        path = Path(value)
        if not path.is_dir():
            return False

        # 如果是目录，获取目录下的所有图片文件
        for file_path in path.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_paths.append(str(file_path))

    # 过滤出存在的图片文件
    valid_paths = [path for path in image_paths if os.path.isfile(path)]

    # 按照图片名称中的index进行排序
    sorted_paths = self._sort_images_by_index(valid_paths)

    return sorted_paths

if __name__ == "__main__":
    print("🚀 开始测试装饰组件...")
    
    # # 首先测试图片排序功能
    # print("\n=== 测试图片排序 ===")
    # test_image_sorting()
    
    # # 然后测试元素查找
    # print("\n=== 测试元素查找 ===")
    # test_find_decoration_elements()
    
    # 最后测试完整功能
    print("\n=== 测试完整功能 ===")
    test_decoration_component()
    #
    # print("\n🎉 所有测试完成！")


