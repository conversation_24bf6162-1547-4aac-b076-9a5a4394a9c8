# Temu 自动化工具环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# ==========================================
# 日志配置
# ==========================================

# 日志级别设置
# 可选值: TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL
# 默认: INFO
LOG_LEVEL=INFO

# 调试模式开关
# 设置为 1、true、yes、on 启用DEBUG级别日志
# 设置为 0、false、no、off 或留空禁用DEBUG级别日志
# 注意: 如果同时设置了 LOG_LEVEL 和 DEBUG，LOG_LEVEL 优先
DEBUG=0

# ==========================================
# 邮件通知配置（可选）
# ==========================================

# SMTP服务器配置
SMTP_SERVER=smtp.qq.com
SMTP_PORT=587

# 发件人邮箱和密码
SENDER_EMAIL=<EMAIL>
EMAIL_PASSWORD=your_app_password

# 默认收件人列表（用逗号分隔）
DEFAULT_RECIPIENTS=<EMAIL>,<EMAIL>

# 邮件模板路径
ALERT_TEMPLATE_PATH=data/alert.html
REPORT_TEMPLATE_PATH=data/report.html

# ==========================================
# 其他配置
# ==========================================

# 浏览器配置
BROWSER_HEADLESS=false
BROWSER_PORT=9001

# 数据文件路径
USER_DATA_PATH=data/users.csv
FIELDS_CONFIG_PATH=config/fileds.json
