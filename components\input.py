"""
文本输入组件

处理普通的文本输入字段
"""

from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class InputComponent(BaseComponent):
    """文本输入组件"""

    def find_field_container(self, field_title: str):
        """
        查找字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            # 策略1: 查找label元素，然后找最近的input容器
            label_selector = f"x://label[contains(text(), '{field_title}')]"
            label_element = self.tab.ele(label_selector, timeout=5)

            if label_element:
                # 从label开始，向上查找包含input的最小容器
                ancestors = label_element.eles("x:./ancestor::div[.//input or .//textarea]", timeout=3)

                # 找到包含最少input选项的容器（最精确的容器）
                best_container = None
                min_input_count = float('inf')

                for ancestor in ancestors:
                    input_elements = ancestor.eles("x:.//input | .//textarea", timeout=2)
                    input_count = len(input_elements)

                    if input_count > 0 and input_count < min_input_count:
                        min_input_count = input_count
                        best_container = ancestor

                if best_container:
                    logger.debug(f"找到字段容器: {field_title} (包含{min_input_count}个input)")
                    return best_container

            # 策略2: 如果策略1失败，使用原来的方法作为备选
            fallback_selector = f"x://*[contains(text(), '{field_title}')]/ancestor::div[.//input or .//textarea][1]"
            container = self.tab.ele(fallback_selector, timeout=5)
            if container:
                logger.debug(f"找到字段容器: {field_title} (备选方法)")
                return container

            logger.warning(f"未找到字段容器: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找字段容器失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充文本输入字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")
            
            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)
            
            # 查找输入框
            input_element = self._find_input_element(field_container, field_title)
            if not input_element:
                error_msg = f"未找到输入框: {field_title}"
                self.log_operation(field_title, "查找输入框", False, error_msg)
                return error_response(error_msg)
            
            # 清空并输入新值
            input_element.clear()
            input_element.input(str(value))
            
            self.log_operation(field_title, f"输入文本: {value}")
            return success_response(f"成功填充字段 '{field_title}': {value}")
            
        except Exception as e:
            error_msg = f"填充文本输入字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)
    
    def _find_input_element(self, field_container, field_title: str):
        """
        在字段容器中查找输入框元素

        Args:
            field_container: 字段容器元素
            field_title: 字段标题

        Returns:
            输入框元素或None
        """
        # 精确的input选择器，基于实际测试验证
        selectors = [
            "x:.//input[@type='text']",  # 主要选择器：文本输入框
            "x:.//input[not(@type)]",    # 备选：无type属性的input
            "x:.//textarea"              # 备选：文本域
        ]

        for selector in selectors:
            try:
                element = field_container.ele(selector, timeout=2)
                if element:
                    logger.debug(f"找到输入框: {field_title} -> {selector}")
                    return element
            except Exception:
                continue

        return None
