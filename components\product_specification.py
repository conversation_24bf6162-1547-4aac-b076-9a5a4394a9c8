#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品规格组件

统一处理所有商品规格数据，包括父规格选择和规格值输入
"""

from typing import Any, Dict, List
from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class ProductSpecificationComponent(BaseComponent):
    """商品规格组件 - 统一处理所有规格数据"""

    def find_parent_spec_container(self, spec_number: int):
        """
        查找指定编号的父规格容器

        Args:
            spec_number: 父规格编号 (1-5)

        Returns:
            容器元素或None
        """
        try:
            spec_label = f"父规格{spec_number}"
            container_selectors = [
                f"x://div[.//span[contains(text(), '{spec_label}')]]",
                f"x://div[contains(@class, 'product-skc') and .//span[contains(text(), '{spec_label}')]]",
                f"x://div[contains(@class, 'specification') and .//span[contains(text(), '{spec_label}')]]"
            ]

            for selector in container_selectors:
                container = self.tab.ele(selector, timeout=3)
                if container:
                    logger.debug(f"找到{spec_label}容器: {selector}")
                    return container

            logger.debug(f"未找到{spec_label}容器")
            return None

        except Exception as e:
            logger.error(f"查找{spec_label}容器失败: {str(e)}")
            return None

    def find_field_container(self, field_title: str):
        """
        查找商品规格的主容器区域（包含所有父规格）

        Args:
            field_title: 字段标题

        Returns:
            容器元素或None
        """
        try:
            # 查找包含父规格1的主容器区域
            container_selectors = [
                f"x://div[.//label[contains(text(), '父规格1')]]",
                f"x://div[.//span[contains(text(), '父规格1')]]",
                f"x://form[.//label[contains(text(), '父规格1')]]",
                f"x://section[.//label[contains(text(), '父规格1')]]"
            ]

            for selector in container_selectors:
                container = self.tab.ele(selector, timeout=3)
                if container:
                    logger.debug(f"找到规格主容器: {selector}")
                    return container

            logger.warning(f"未找到规格主容器")
            return None

        except Exception as e:
            logger.error(f"查找规格主容器失败: {str(e)}")
            return None

    def validate_required_field(self, field_title: str, field_data: Dict[str, Any]):
        """
        验证商品规格必填字段

        Args:
            field_title: 字段标题
            field_data: 字段数据

        Returns:
            ApiResponse: 如果验证失败返回错误响应，成功返回None
        """
        require = field_data.get("require", 0)
        if require == 1:
            value = field_data.get("value")

            # 检查值是否为空
            if not value or not isinstance(value, str):
                logger.error(f"必填字段 '{field_title}' 的值不能为空")
                return error_response(
                    f"必填字段 '{field_title}' 的值不能为空",
                    data={"missing_field": field_title, "field_data": field_data}
                )

            # 验证JSON格式的规格数据
            try:
                import json
                specifications = json.loads(value)

                if not specifications or len(specifications) == 0:
                    logger.error(f"必填字段 '{field_title}' 必须包含至少一个规格")
                    return error_response(
                        f"必填字段 '{field_title}' 必须包含至少一个规格",
                        data={"missing_field": field_title, "field_data": field_data}
                    )

            except json.JSONDecodeError:
                logger.error(f"必填字段 '{field_title}' 的JSON格式无效")
                return error_response(
                    f"必填字段 '{field_title}' 的JSON格式无效",
                    data={"missing_field": field_title, "field_data": field_data}
                )

        return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充商品规格数据

        Args:
            field_title: 字段标题
            field_data: 字段数据，包含specifications列表

        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段（使用重写的验证方法）
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error

            value = field_data.get("value")
            if not value:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")

            # 解析规格数据：JSON字符串格式 '[{"name": "风格", "values": "测试"}]'
            import json
            try:
                # 解析JSON字符串，直接是规格数组
                specifications = json.loads(value)

                # 标准化规格数据格式
                normalized_specs = []
                for spec in specifications:
                    name = spec.get("name", "")
                    values = spec.get("values", "")

                    # 处理values字段：将字符串按逗号分割为数组
                    if isinstance(values, str) and values.strip():
                        values_list = [v.strip() for v in values.split(',') if v.strip()]
                        if name and values_list:
                            normalized_specs.append({
                                "name": name,
                                "values": values_list
                            })

                specifications = normalized_specs

            except json.JSONDecodeError:
                logger.error(f"商品规格数据JSON格式无效: {value}")
                return error_response(f"商品规格数据JSON格式无效")
            if not specifications:
                self.log_operation(field_title, "跳过填充（无规格数据）")
                return success_response(f"字段 '{field_title}' 无规格数据")
            
            # 查找商品规格容器
            container = self.find_field_container(field_title)
            if not container:
                error_msg = f"未找到商品规格容器: {field_title}"
                self.log_operation(field_title, "查找容器", False, error_msg)
                return error_response(error_msg)
            
            logger.info(f"开始填充 {len(specifications)} 个商品规格")
            
            # 逐个处理每个规格
            success_count = 0
            for i, spec in enumerate(specifications, 1):
                spec_name = spec.get("name", "")
                spec_values = spec.get("values", [])
                
                if not spec_name or not spec_values:
                    logger.warning(f"跳过无效规格 {i}: name={spec_name}, values={spec_values}")
                    continue
                
                logger.info(f"处理规格 {i}: {spec_name} = {spec_values}")
                
                # 处理父规格选择
                if self._handle_parent_specification(container, i, spec_name):
                    # 处理规格值输入
                    if self._handle_specification_values(container, i, spec_values):
                        success_count += 1
                        logger.info(f"✅ 规格 {i} 填充成功: {spec_name}")
                    else:
                        logger.error(f"❌ 规格 {i} 值填充失败: {spec_name}")
                else:
                    logger.error(f"❌ 规格 {i} 父规格选择失败: {spec_name}")
            
            if success_count > 0:
                # 输入完成后点击页面空白区域触发数据保存
                self._click_blank_area()
                self.log_operation(field_title, f"填充成功: {success_count}/{len(specifications)} 个规格")
                return success_response(f"成功填充 {success_count}/{len(specifications)} 个商品规格")
            else:
                error_msg = f"所有规格填充失败"
                self.log_operation(field_title, "填充数据", False, error_msg)
                return error_response(error_msg)
                
        except Exception as e:
            error_msg = f"填充商品规格失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            logger.exception("商品规格填充异常")
            return error_response(error_msg)

    def _handle_parent_specification(self, container, spec_number: int, spec_name: str) -> bool:
        """
        处理父规格选择

        Args:
            container: 规格容器
            spec_number: 规格编号（1-5）
            spec_name: 规格名称

        Returns:
            是否成功
        """
        try:
            # 确保对应的父规格字段存在
            if spec_number > 1:
                if not self._ensure_parent_spec_exists(spec_number):
                    logger.error(f"无法创建父规格{spec_number}")
                    return False

            # 在指定的父规格中选择选项
            return self._select_parent_spec_option(spec_number, spec_name)

        except Exception as e:
            logger.error(f"处理父规格 {spec_number} 失败: {str(e)}")
            return False

    def _ensure_parent_spec_exists(self, spec_number: int) -> bool:
        """
        确保指定编号的父规格字段存在

        Args:
            spec_number: 父规格编号（2-5）

        Returns:
            bool: 是否成功
        """
        try:
            # 检查父规格是否已存在
            existing_container = self.find_parent_spec_container(spec_number)
            if existing_container:
                logger.debug(f"父规格{spec_number}已存在")
                return True

            # 查找"添加父规格 X"按钮
            add_button_selectors = [
                f"x://button[contains(text(), '添加父规格 {spec_number}')]",
                f"x://span[contains(text(), '添加父规格 {spec_number}')]",
                f"x://*[contains(text(), '添加父规格 {spec_number}')]",
                f"x://button[contains(text(), '添加父规格')]",
                f"x://span[contains(text(), '添加父规格')]"
            ]

            add_button = None
            for selector in add_button_selectors:
                add_button = self.tab.ele(selector, timeout=2)
                if add_button:
                    break

            if not add_button:
                logger.error(f"未找到添加父规格按钮")
                return False

            # 点击添加父规格按钮
            logger.info(f"点击添加父规格按钮创建父规格{spec_number}")
            add_button.click()
            time.sleep(1)

            # 验证新的父规格是否创建成功
            new_container = self.find_parent_spec_container(spec_number)
            if new_container:
                logger.info(f"✅ 父规格{spec_number}创建成功")
                return True
            else:
                logger.error(f"❌ 父规格{spec_number}创建失败")
                return False

        except Exception as e:
            logger.error(f"确保父规格{spec_number}存在失败: {str(e)}")
            return False

    def _select_parent_spec_option(self, spec_number: int, spec_name: str) -> bool:
        """
        在指定的父规格中选择选项

        Args:
            spec_number: 父规格编号（1-5）
            spec_name: 规格名称

        Returns:
            bool: 是否成功
        """
        try:
            # 找到对应的父规格容器
            spec_container = self.find_parent_spec_container(spec_number)
            if not spec_container:
                logger.error(f"未找到父规格{spec_number}容器")
                return False

            # 查找父规格的select元素
            select_selectors = [
                f"x:.//div[@data-testid='beast-core-select']",
                f"x:.//select",
                f"x:.//div[contains(@class, 'select')]"
            ]

            select_element = None
            for selector in select_selectors:
                select_element = spec_container.ele(selector, timeout=2)
                if select_element:
                    break

            if not select_element:
                logger.error(f"未找到父规格{spec_number}的选择器")
                return False

            # 点击打开下拉菜单
            select_element.click()
            time.sleep(1)

            # 查找并选择对应的选项
            option_selectors = [
                f"x://div[contains(@class, 'option') and contains(text(), '{spec_name}')]",
                f"x://li[contains(@class, 'option') and contains(text(), '{spec_name}')]",
                f"x://div[@role='option' and contains(text(), '{spec_name}')]"
            ]

            for selector in option_selectors:
                option = self.tab.ele(selector, timeout=3)
                if option:
                    option.click()
                    time.sleep(1)
                    logger.info(f"✅ 成功选择父规格{spec_number}: {spec_name}")
                    return True

            logger.error(f"未找到父规格选项: {spec_name}")
            return False

        except Exception as e:
            logger.error(f"选择父规格{spec_number}选项失败: {str(e)}")
            return False

    def _handle_specification_values(self, container, spec_number: int, values: List[str]) -> bool:
        """
        处理规格值输入

        Args:
            container: 规格容器
            spec_number: 规格编号
            values: 规格值列表

        Returns:
            是否成功
        """
        try:
            # 过滤空值
            valid_values = [v.strip() for v in values if v and str(v).strip()]
            if not valid_values:
                logger.warning(f"规格 {spec_number} 没有有效值")
                return False

            logger.debug(f"父规格{spec_number} 需要输入 {len(valid_values)} 个值: {valid_values}")

            # 找到对应的父规格容器
            spec_container = self.find_parent_spec_container(spec_number)
            if not spec_container:
                logger.error(f"未找到父规格{spec_number}容器")
                return False

            success_count = 0
            for i, value in enumerate(valid_values):
                if i == 0:
                    # 第一个值：查找现有的input
                    if self._input_first_value(spec_container, spec_number, value):
                        success_count += 1
                else:
                    # 后续值：添加新的input
                    if self._add_and_input_value(spec_container, spec_number, value):
                        success_count += 1
            
            return success_count == len(valid_values)
            
        except Exception as e:
            logger.error(f"处理规格值输入失败: {str(e)}")
            return False

    def _input_first_value(self, container, spec_number: int, value: str) -> bool:
        """输入第一个规格值"""
        try:
            # 查找对应规格的input元素
            input_selectors = [
                f"x:.//input[position()={spec_number}]",
                f"x:.//div[position()={spec_number}]//input",
                f"x:.//input[@placeholder='请输入'][position()={spec_number}]"
            ]
            
            for selector in input_selectors:
                inputs = container.eles(selector, timeout=2)
                if inputs and len(inputs) >= spec_number:
                    input_element = inputs[spec_number - 1]
                    input_element.clear()
                    input_element.input(value)
                    self.tab.wait(0.5)
                    logger.debug(f"成功输入第一个规格值: {value}")
                    return True
            
            logger.error(f"未找到规格 {spec_number} 的input元素")
            return False
            
        except Exception as e:
            logger.error(f"输入第一个规格值失败: {str(e)}")
            return False

    def _add_and_input_value(self, container, spec_number: int, value: str) -> bool:
        """添加并输入新的规格值"""
        try:
            # 查找添加子规格按钮
            add_button_selectors = [
                f"x:.//button[contains(text(), '继续添加子规格')]",
                f"x:.//div[contains(text(), '继续添加子规格')]",
                f"x:.//span[contains(text(), '继续添加子规格')]"
            ]
            
            for selector in add_button_selectors:
                buttons = container.eles(selector, timeout=2)
                if buttons:
                    # 选择对应规格的按钮
                    if len(buttons) >= spec_number:
                        button = buttons[spec_number - 1]
                    else:
                        button = buttons[-1]  # 使用最后一个按钮
                    
                    button.click()
                    self.tab.wait(1)
                    
                    # 查找新添加的input并输入值
                    new_inputs = container.eles("x:.//input[@placeholder='请输入']", timeout=2)
                    if new_inputs:
                        new_input = new_inputs[-1]  # 使用最后一个input
                        new_input.input(value)
                        self.tab.wait(0.5)
                        logger.debug(f"成功添加并输入规格值: {value}")
                        return True
            
            logger.error(f"未找到添加子规格按钮")
            return False
            
        except Exception as e:
            logger.error(f"添加并输入规格值失败: {str(e)}")
            return False

    def _click_blank_area(self):
        """点击页面空白区域触发数据保存"""
        try:
            logger.debug("点击页面空白区域触发数据保存")
            
            # 尝试多种方式点击空白区域
            strategies = [
                lambda: self.tab.ele("tag:body").click(),
                lambda: self.tab.run_js("document.body.click();"),
                lambda: self.tab.actions.click((100, 100)),
                lambda: self.tab.key("Escape")
            ]
            
            for i, strategy in enumerate(strategies, 1):
                try:
                    strategy()
                    self.tab.wait(0.5)
                    logger.debug(f"成功使用策略 {i} 点击空白区域")
                    return
                except:
                    continue
            
            logger.warning("所有空白区域点击策略都失败了")
            
        except Exception as e:
            logger.error(f"点击空白区域失败: {str(e)}")
