import os
from os import environ

from dotenv import load_dotenv
from DrissionPage import Chromium, ChromiumOptions

load_dotenv()

USER_DATA_PATH = environ.get("USER_DATA_PATH")


# 自动获取系统浏览器
def get_page_auto():
    co = ChromiumOptions().auto_port()
    return Chromium(co)


# 从已有的浏览器用户进行调试
def get_page_by_browser_user():
    co = ChromiumOptions()
    co.use_system_user_path(on_off=True)
    return Chromium(addr_or_opts=co)


# 根据浏览器名称获取浏览器配置
def get_page_by_id(id: str) -> Chromium:
    """
    根据浏览器ID创建浏览器页面实例

    Args:
        id: 浏览器标识
    Returns:
        Chromium: 配置好的浏览器页面实例
    """
    co = ChromiumOptions()
    co.set_argument("--accept-lang=en-US,en")
    co.set_argument("--lang=en-US")
    co.set_pref("credentials_enable_service", False)

    user_data_path = rf"{USER_DATA_PATH}/{id}"
    if not os.path.exists(user_data_path):
        os.makedirs(user_data_path)

    # 注意：local_port 不能和系统浏览器端口冲突
    local_port = 9000 + int(id)
    co.set_paths(user_data_path=user_data_path, local_port=local_port)
    return Chromium(co)
