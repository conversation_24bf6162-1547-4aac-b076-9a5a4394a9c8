"""
详情图文装饰组件

处理商品详情页的图文装饰字段
"""

import os
import re
from pathlib import Path
from typing import Any, Dict, List

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class DecorationComponent(BaseComponent):
    """详情图文装饰组件"""

    def find_field_container(self, field_title: str):
        return self.tab.ele("x://button[.='开始装修']")


    def is_opened(self):
        """检查装修页面是否已打开"""
        return self.tab.ele("x://div[@data-testid='beast-core-modal-inner']", timeout=2)

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充详情图文装饰字段

        Args:
            field_title: 字段标题
            field_data: 字段数据

        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error

            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")

            # 获取图片文件路径
            image_paths = self._get_decoration_images(value)
            if not image_paths:
                error_msg = f"未找到有效的装饰图片: {value}"
                self.log_operation(field_title, "验证装饰图片", False, error_msg)
                return error_response(error_msg)

            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            try:
                scroll_js = "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});"
                self.tab.run_js(scroll_js, field_container)
                
            except Exception as e:
                logger.error(f"滚动到元素失败: {e}")


            for _ in range(3):
                try:
                    # 查找字段容器
                    self.tab.wait(2)
                    field_container = self.find_field_container(field_title)
                    if not field_container:
                       continue

                    field_container.click()
                    is_success = self.is_opened()
                    if not is_success:
                        logger.warning(f"详情页装修未打开")
                        continue

                    break
                except Exception as e:
                    logger.error(f"点击元素失败: {e}")
            else:
                error_msg = "尝试 3 次后详情页装修仍未打开"
                logger.error(error_msg)
                return error_response(error_msg)

            # 依次添加图片
            added_count = self._add_decoration_images_sequentially(image_paths, field_title)
            self.log_operation(field_title, f"添加装饰图片: {added_count}/{len(image_paths)} 张")

            if added_count == len(image_paths):
                self.tab.ele("x://button[.='保存']", timeout=3).click()

                # 获取装修图片数量
                current_count = self._get_current_decoration_count()
                if current_count == added_count:
                    logger.success(f"保存后装修区域共有 {current_count} 张图片")
                    return success_response(f"成功添加 {added_count} 张装饰图片，当前共有 {current_count} 张图片")
                else:
                    return error_response(f"添加图片{current_count}/{len(image_paths)}, 未全部添加完成...")

            return error_response(f"添加失败, 添加 {added_count}/{len(image_paths)} 张装饰图片")

            
        except Exception as e:
            error_msg = f"填充详情图文装饰字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)


    def _add_decoration_images_sequentially(self, image_paths: List[str], field_title: str) -> int:
        """
        依次添加装饰图片

        Args:
            image_paths: 按index排序的图片路径列表
            field_title: 字段标题

        Returns:
            成功添加的图片数量
        """
        added_count = 0

        try:
            # 查找图片组件按钮
            image_button = self.tab.ele("x://div[contains(@draggable, 'true') and .//span[contains(text(), '图片')]]", timeout=5)
            if not image_button:
                logger.error("未找到图片组件按钮")
                return 0

            for index, image_path in enumerate(image_paths, 1):
                logger.debug(f"开始添加第 {index} 张装饰图片: {os.path.basename(image_path)}")

                # 点击图片组件按钮
                image_button.click()
                self.tab.wait(2)

                # 打开素材中心并上传图片
                if self._upload_image_to_material_center(image_path, field_title, index):
                    # 选择刚上传的图片
                    added_count += 1
                    logger.info(f"✅ 装饰图片: 添加第 {index} 张")
                else:
                    logger.error(f"上传第 {index} 张装饰图片失败")

                # 等待处理完成
                self.tab.wait(1)

            return added_count

        except Exception as e:
            logger.error(f"依次添加装饰图片失败: {str(e)}")
            return added_count

    def _upload_image_to_material_center(self, image_path: str, field_title: str, index: int) -> bool:
        """
        上传图片到素材中心

        Args:
            image_path: 图片路径
            field_title: 字段标题
            index: 图片索引

        Returns:
            是否成功上传
        """
        try:
            # 查找素材中心按钮
            material_button = self.tab.ele("x://button[.='从素材中心添加']", timeout=5)
            if not material_button:
                logger.error("未找到素材中心按钮")
                return False

            # 点击素材中心
            material_button.click()
            self.tab.wait(3)

            image_name = os.path.basename(image_path).split('.')[0]
            if not image_name:
                logger.error("获取图片名失败...")
                return False

            result = self._select_preview_image(image_name)
            if not result:
                logger.error("选择图片失败...")
                return False

            logger.debug(f"成功选择第 {index} 张装饰图片到素材中心: {os.path.basename(image_path)}")
            return True

        except Exception as e:
            logger.error(f"上传第 {index} 张装饰图片到素材中心失败: {str(e)}")
            return False

    def _select_preview_image(self, image_name: str) -> bool:
        """
        选择图片预览图

        Args:
            image_name: 图片名

        Returns:
            是否选择成功
        """
        try:
            container = self.tab.ele(
                f"x://div[contains(@class, 'index-module_cardContainer') and .//div[contains(text(), '{image_name}')]]",
                timeout=2)
            if not container:
                logger.warning(f"未找到图片容器: {image_name}")
                return False

            container.click()
            self.tab.wait(0.5)

            logger.debug("正在确认选择...")
            confirm_btn = self.tab.ele("x://button[.='确认' and @data-testid='beast-core-button']", timeout=2)
            if not confirm_btn:
                logger.warning("未找到确认按钮")
                return False

            # 点击确认按钮
            confirm_btn.click()
            self.tab.wait(2)
            return True

        except Exception as e:
            logger.error(f"上传预览图失败: {str(e)}")
            return False

    def _get_current_decoration_count(self) -> int:
        """
        获取装修图片数量
        根据页面元素判断当前已添加的装饰图片数量

        Returns:
            当前已添加的装饰图片数量
        """
        try:
            # 查找预览区域中的图片元素
            preview_images = self.tab.eles("x://div[contains(@class,'preview_view')]//div[contains(@class,'preview_item')]//img")
            if preview_images:
                count = len(preview_images)
                logger.debug(f"通过预览区域找到 {count} 张装饰图片")
                return count

            logger.debug("未找到装饰图片元素，返回数量为 0")
            return 0

        except Exception as e:
            logger.error(f"获取装饰图片数量失败: {str(e)}")
            return 0

    def _get_decoration_images(self, value) -> List[str]:
        """
        获取装饰图片文件路径列表，按照图片名称中的index排序

        Args:
            value: 装饰图片路径值

        Returns:
            按index排序的有效装饰图片文件路径列表
        """
        image_paths = []
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

        if isinstance(value, str):
            path = Path(value)
            if path.is_dir():
                # 如果是目录，获取目录下的所有图片文件
                for file_path in path.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                        image_paths.append(str(file_path))
            elif path.is_file() and path.suffix.lower() in image_extensions:
                # 如果是图片文件，直接添加
                image_paths.append(str(path))
        elif isinstance(value, (list, tuple, set)):
            # 如果是集合类型，逐个处理
            for item in value:
                image_paths.extend(self._get_decoration_images(item))
        elif hasattr(value, '__fspath__'):
            # 如果是路径对象
            image_paths.extend(self._get_decoration_images(str(value)))

        # 过滤出存在的图片文件
        valid_paths = [path for path in image_paths if os.path.isfile(path)]

        # 按照图片名称中的index进行排序
        sorted_paths = self._sort_images_by_index(valid_paths)

        return sorted_paths

    def _sort_images_by_index(self, image_paths: List[str]) -> List[str]:
        """
        按照图片名称中的index进行排序

        Args:
            image_paths: 图片路径列表

        Returns:
            按index排序的图片路径列表
        """
        def extract_sort_key(path: str) -> tuple:
            """从文件名中提取排序键值"""
            filename = os.path.basename(path)
            # 尝试多种数字提取模式
            patterns = [
                r'(\d+)\.(?:jpg|jpeg|png|gif|bmp|webp)$',  # 文件名末尾的数字
                r'_(\d+)\.(?:jpg|jpeg|png|gif|bmp|webp)$',  # 下划线后的数字
                r'-(\d+)\.(?:jpg|jpeg|png|gif|bmp|webp)$',  # 连字符后的数字
                r'(\d+)_',  # 数字后跟下划线
                r'(\d+)-',  # 数字后跟连字符
                r'(\d+)',   # 文件名中的任意数字
            ]

            for pattern in patterns:
                match = re.search(pattern, filename, re.IGNORECASE)
                if match:
                    index = int(match.group(1))
                    # 返回 (索引, 文件名) 作为排序键，确保同一索引的文件按文件名排序
                    return (index, filename)

            # 如果没有找到数字，返回一个大数值，使其排在最后
            return (999999, filename)

        try:
            # 按提取的索引和文件名排序
            sorted_paths = sorted(image_paths, key=extract_sort_key)

            # 记录排序结果
            # logger.debug("图片按index排序结果:")
            # for i, path in enumerate(sorted_paths):
            #     filename = os.path.basename(path)
            #     sort_key = extract_sort_key(path)
                # logger.debug(f"  {i+1}. {filename} (index: {sort_key[0]})")

            return sorted_paths

        except Exception as e:
            logger.error(f"图片排序失败: {str(e)}")
            # 如果排序失败，返回按文件名排序的结果
            return sorted(image_paths)

    def _upload_decoration_image(self, upload_buttons, image_path: str, field_title: str, index: int) -> bool:
        """
        上传单张装饰图片
        
        Args:
            upload_buttons: 上传按钮列表
            image_path: 图片路径
            field_title: 字段标题
            index: 图片索引
            
        Returns:
            是否成功上传
        """
        try:
            for upload_button in upload_buttons:
                try:
                    # 如果是input[type=file]，直接设置文件
                    if upload_button.tag.lower() == 'input' and upload_button.attr('type') == 'file':
                        upload_button.input(image_path)
                        logger.debug(f"添加第 {index} 张装饰图片: {os.path.basename(image_path)}")
                        return True
                    
                    # 如果是按钮，点击后处理文件选择
                    upload_button.click()
                    self.tab.wait(1)
                    
                    # 这里可能需要处理系统文件选择对话框
                    logger.warning(f"需要手动处理文件选择对话框: {field_title} 第 {index} 张装饰图片")
                    return False
                    
                except Exception as e:
                    logger.debug(f"尝试上传按钮失败: {str(e)}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"添加第 {index} 张装饰图片失败: {str(e)}")
            return False

