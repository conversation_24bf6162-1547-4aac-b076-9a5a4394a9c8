"""
文本域组件

处理多行文本输入字段
"""

from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class TextareaComponent(BaseComponent):
    """文本域组件"""

    def find_field_container(self, field_title: str):
        return self.tab.ele("x://div[@id='productName']//textarea", timeout=5)

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充文本域字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            value = field_data.get("value")
            require = field_data.get("require")
            if value is None and require == 1:
                return error_response(f"字段 '{field_title}' 不能为空")

            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            # 清空并输入新值
            field_container.clear(True)
            field_container.input(str(value))

            self.log_operation(field_title, f"输入文本: {value}")
            return success_response(f"成功填充字段 '{field_title}': {value}")
            
        except Exception as e:
            error_msg = f"填充文本域字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)
