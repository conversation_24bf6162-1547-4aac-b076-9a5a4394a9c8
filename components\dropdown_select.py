"""
通用下拉选择组件

处理各种下拉选择框字段，如敏感属性、SKU分类等
"""

from typing import Any, Dict
from loguru import logger
from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class DropdownSelectComponent(BaseComponent):
    """通用下拉选择组件"""

    def _execute_js_xpath(self, xpath_expression: str):
        """
        执行JavaScript XPath表达式

        Args:
            xpath_expression: XPath表达式

        Returns:
            执行结果
        """
        try:
            # 使用JSON.stringify来安全地传递XPath表达式，避免引号冲突
            js_code = f"""
            var xpath = {repr(xpath_expression)};
            return document.evaluate(
                xpath,
                document,
                null,
                XPathResult.NUMBER_TYPE,
                null
            ).numberValue;
            """
            result = self.tab.run_js(js_code)
            logger.debug(f"JavaScript XPath执行结果: {xpath_expression} -> {result}")
            return result
        except Exception as e:
            logger.error(f"JavaScript XPath执行失败: {xpath_expression} - {str(e)}")
            return None

    def find_field_container(self, field_title: str):
        """
        查找下拉选择框字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            # 方法1: 使用精确XPath匹配查找表格中的字段位置
            js_xpath = f"count(//th[.//*[contains(normalize-space(text()), '{field_title}')]]/preceding-sibling::th) + 1"
            column_count = self._execute_js_xpath(js_xpath)

            if column_count and column_count > 0:
                column_index = int(column_count)
                logger.debug(f"找到字段列索引: {field_title} -> 第{column_index}列")

                # 根据列索引查找对应的下拉选择框
                table_selector = f"x://table[contains(@class, 'performance-table')]//tbody//td[{column_index}]//div[@data-testid='beast-core-input']"
                element = self.tab.ele(table_selector, timeout=3)
                if element:
                    logger.debug(f"找到下拉选择框(包含匹配): {field_title}")
                    return element

            # 方法2: 使用xpath查找包含字段标题的表格行中的选择框
            selector1 = f"x://table[.//span[contains(text(), '{field_title}')]]//div[@data-testid='beast-core-input']"
            elements = self.tab.eles(selector1, timeout=3)

            if elements:
                element = elements[0]
                logger.debug(f"找到下拉选择框(方法2): {field_title}")
                return element

            # 方法3: 查找包含字段标题的div容器中的选择框
            selector2 = f"x://div[contains(text(), '{field_title}')]/following-sibling::*//div[@data-testid='beast-core-input']"
            element = self.tab.ele(selector2, timeout=3)

            if element:
                logger.debug(f"找到下拉选择框(方法3): {field_title}")
                return element

            # 方法4: 查找包含字段标题的父容器中的选择框
            selector3 = f"x://div[.//text()[contains(., '{field_title}')]]//div[@data-testid='beast-core-input']"
            element = self.tab.ele(selector3, timeout=3)

            if element:
                logger.debug(f"找到下拉选择框(方法4): {field_title}")
                return element

            logger.warning(f"未找到下拉选择框: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找下拉选择框失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充下拉选择字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")
            
            # 查找下拉选择框
            select_element = self.find_field_container(field_title)
            if not select_element:
                error_msg = f"未找到下拉选择框: {field_title}"
                self.log_operation(field_title, "查找下拉选择框", False, error_msg)
                return error_response(error_msg)

            # 滚动到元素位置，确保元素可见
            logger.debug(f"滚动到下拉选择框: {field_title}")



            self.tab.run_js("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", select_element)
            self.tab.wait(1)  # 等待滚动完成

            # 点击打开下拉菜单
            self.tab.run_js("arguments[0].click();", select_element)
            self.tab.wait(3)  # 等待下拉菜单展开

            # 查找并选择选项
            option_selected = self._select_option(value, field_title)
            if not option_selected:
                error_msg = f"未找到选项: {value}"
                self.log_operation(field_title, "选择选项", False, error_msg)
                return error_response(error_msg)

            self.log_operation(field_title, f"选择选项: {value}")
            return success_response(f"成功填充下拉选择字段 '{field_title}': {value}")

        except Exception as e:
            error_msg = f"填充下拉选择字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)

    def _select_option(self, value: str, field_title: str) -> bool:
        """
        选择下拉菜单中的选项

        Args:
            value: 要选择的值
            field_title: 字段标题

        Returns:
            是否成功选择
        """
        # 精确的选项选择器 - 只在当前打开的下拉框中查找
        option_selector = f"x://li[@role='option' and contains(.,'{value}')]"

        try:
            option = self.tab.ele(option_selector, timeout=5)
            if option:
                # 使用JavaScript点击确保可靠性
                self.tab.run_js("arguments[0].click();", option)
                logger.debug(f"选择敏感属性选项: {field_title} -> {value}")
                self.tab.wait(1)  # 等待选择生效
                return True
        except Exception as e:
            logger.debug(f"选择敏感属性选项失败: {str(e)}")

        return False
