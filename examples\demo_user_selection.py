#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户选择功能演示脚本

这个脚本演示了新的中文选择框功能，可以独立运行来测试用户选择界面。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import click
from utils.user_manager import UserManager


def demo_user_selection():
    """演示用户选择功能"""
    click.echo("🎯 用户选择功能演示")
    click.echo("=" * 50)
    
    # 初始化用户管理器
    user_manager = UserManager()
    
    # 加载用户数据
    click.echo("📋 正在加载用户数据...")
    if not user_manager.load_users():
        click.echo("❌ 加载用户数据失败，请检查 data/users.csv 文件")
        return
    
    if user_manager.get_user_count() == 0:
        click.echo("❌ 没有可用的用户数据，请先配置 data/users.csv 文件")
        return
    
    # 显示用户选择界面
    selected_index = user_manager.prompt_user_selection()
    
    if selected_index:
        user_info = user_manager.get_user_by_index(selected_index)
        click.echo(f"\n🎉 演示完成！")
        click.echo(f"选择的用户信息:")
        click.echo(f"  序号: {selected_index}")
        click.echo(f"  用户名: {user_info.get('username', '未知')}")
        click.echo(f"  手机号: {user_manager._mask_phone(user_info.get('phone', ''))}")
    else:
        click.echo("\n👋 演示结束，未选择用户")


def demo_simple_selection():
    """演示简单选择功能"""
    click.echo("🎯 简单用户选择功能演示")
    click.echo("=" * 50)
    
    # 初始化用户管理器
    user_manager = UserManager()
    
    # 加载用户数据
    click.echo("📋 正在加载用户数据...")
    if not user_manager.load_users():
        click.echo("❌ 加载用户数据失败，请检查 data/users.csv 文件")
        return
    
    if user_manager.get_user_count() == 0:
        click.echo("❌ 没有可用的用户数据，请先配置 data/users.csv 文件")
        return
    
    # 显示简单选择界面
    selected_index = user_manager.prompt_user_selection_simple()
    
    if selected_index:
        user_info = user_manager.get_user_by_index(selected_index)
        click.echo(f"\n🎉 演示完成！")
        click.echo(f"选择的用户信息:")
        click.echo(f"  序号: {selected_index}")
        click.echo(f"  用户名: {user_info.get('username', '未知')}")
        click.echo(f"  手机号: {user_manager._mask_phone(user_info.get('phone', ''))}")
    else:
        click.echo("\n👋 演示结束，未选择用户")


@click.group()
def cli():
    """用户选择功能演示工具"""
    pass


@cli.command()
def chinese():
    """演示中文选择框界面"""
    demo_user_selection()


@cli.command()
def simple():
    """演示简单选择界面"""
    demo_simple_selection()


@cli.command()
def list():
    """显示用户列表"""
    user_manager = UserManager()
    
    click.echo("📋 正在加载用户数据...")
    if not user_manager.load_users():
        click.echo("❌ 加载用户数据失败，请检查 data/users.csv 文件")
        return
    
    if user_manager.get_user_count() == 0:
        click.echo("❌ 没有可用的用户数据，请先配置 data/users.csv 文件")
        return
    
    user_manager.display_users()
    click.echo(f"\n📊 总计: {user_manager.get_user_count()} 个用户")


@cli.command()
def info():
    """显示功能信息"""
    click.echo("🎯 用户选择功能说明")
    click.echo("=" * 50)
    click.echo("✨ 新功能特点:")
    click.echo("  • 美观的中文选择框界面")
    click.echo("  • 彩色显示和图标提示")
    click.echo("  • 手机号隐私保护")
    click.echo("  • 用户状态验证")
    click.echo("  • 确认选择机制")
    click.echo("  • 完善的错误处理")
    click.echo("\n🚀 使用方法:")
    click.echo("  python demo_user_selection.py chinese  # 中文选择框")
    click.echo("  python demo_user_selection.py simple   # 简单选择")
    click.echo("  python demo_user_selection.py list     # 显示用户列表")
    click.echo("  python demo_user_selection.py info     # 显示此信息")
    click.echo("\n📁 配置文件:")
    click.echo("  data/users.csv - 用户数据文件")
    click.echo("  格式: index,phone,password,username")


if __name__ == "__main__":
    cli()
