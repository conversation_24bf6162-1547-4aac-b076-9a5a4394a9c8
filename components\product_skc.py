"""
商品规格组件

处理父规格选择和商品规格输入的配对组件
支持多值输入（逗号分隔，一个一行）
"""

from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class ProductSkcComponent(BaseComponent):
    """商品规格组件"""

    def find_field_container(self, field_title: str):
        """
        查找product-skc容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            # 精确查找product-skc容器
            container_selector = "x://div[contains(@class,'product-skc')]"
            container = self.tab.ele(container_selector, timeout=5)
            
            if container:
                logger.debug(f"找到product-skc容器: {field_title}")
                return container
            
            logger.warning(f"未找到product-skc容器: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找product-skc容器失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充商品规格字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")
            
            # 查找product-skc容器
            container = self.find_field_container(field_title)
            if not container:
                error_msg = f"未找到product-skc容器: {field_title}"
                self.log_operation(field_title, "查找容器", False, error_msg)
                return error_response(error_msg)

            try:
                self.tab.run_js("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", container)
                self.tab.wait(1)  # 等待滚动完成
            except Exception as e:
                pass

            # 根据字段标题判断处理类型
            if "父规格" in field_title:
                # 处理父规格选择
                result = self._handle_parent_spec_select(container, field_title, value)
            elif "商品规格" in field_title:
                # 处理商品规格输入（支持多值）
                result = self._handle_spec_input(container, field_title, value)
            else:
                error_msg = f"未知的字段类型: {field_title}"
                self.log_operation(field_title, "判断字段类型", False, error_msg)
                return error_response(error_msg)

            # 删除弹窗元素
            try:
                self.tab.wait(2)
                iknow_btn = self.tab.ele("x://div[@data-testid='beast-core-modal-inner']//button[.='我知道了']", timeout=3)
                if iknow_btn:
                    iknow_btn.click()
                    self.tab.wait(2)
            except Exception as e:
                pass

            if result:
                self.log_operation(field_title, f"填充成功: {value}")
                return success_response(f"成功填充字段 '{field_title}': {value}")
            else:
                error_msg = f"填充失败: {field_title}"
                self.log_operation(field_title, "填充数据", False, error_msg)
                return error_response(error_msg)

        except Exception as e:
            error_msg = f"填充商品规格字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)

    def _handle_parent_spec_select(self, container, field_title: str, value: str) -> bool:
        """
        处理父规格选择

        Args:
            container: product-skc容器
            field_title: 字段标题
            value: 要选择的值

        Returns:
            是否成功选择
        """
        try:
            # 检查值是否为空
            if not value or not value.strip():
                logger.debug(f"跳过空值父规格: {field_title}")
                return True  # 空值视为成功，但不执行任何操作

            # 根据父规格编号查找对应的select元素
            spec_number = self._extract_spec_number(field_title)
            if spec_number is None:
                logger.error(f"无法提取规格编号: {field_title}")
                return False

            # 查找所有可用的select元素
            select_selector = f"x:.//div[@data-testid='beast-core-select']"
            select_elements = container.eles(select_selector, timeout=5)

            logger.debug(f"找到 {len(select_elements)} 个select元素，需要第 {spec_number} 个")

            # 如果select元素不够，可能需要先添加父规格
            if len(select_elements) < spec_number:
                # 尝试查找"添加父规格"按钮（区别于"添加子规格"）
                add_parent_success = self._add_parent_spec(container, spec_number - len(select_elements))

                if add_parent_success:
                    # 重新查找select元素
                    select_elements = container.eles(select_selector, timeout=5)
                    logger.debug(f"添加父规格后找到 {len(select_elements)} 个select元素")
                else:
                    logger.warning(f"无法添加父规格")

            if len(select_elements) < spec_number:
                logger.error(f"仍然未找到第{spec_number}个select元素")
                return False

            # 获取对应的select元素（索引从0开始）
            select_element = select_elements[spec_number - 1]

            # 点击打开下拉菜单
            self.tab.run_js("arguments[0].click();", select_element)
            self.tab.wait(3)

            exact_option_selector = f"x://li[@role='option']/span[.='{value}']"
            option = self.tab.ele(exact_option_selector, timeout=3)

            # 如果精确匹配失败，尝试包含匹配但排除明显不匹配的
            if not option:
                logger.error(f"未找到选项: {value}")
                return False

            self.tab.run_js("arguments[0].click();", option)
            logger.debug(f"成功选择父规格: {field_title} -> {value}")
            self.tab.wait(2)  # 等待页面更新
            return True

        except Exception as e:
            logger.error(f"处理父规格选择失败: {str(e)}")
            return False

    def _handle_spec_input(self, container, field_title: str, value: str) -> bool:
        """
        处理商品规格输入（支持多值，逗号分隔，动态添加子规格）

        Args:
            container: product-skc容器
            field_title: 字段标题
            value: 要输入的值（支持逗号分隔的多值）

        Returns:
            是否成功输入
        """
        try:
            # 处理value可能是字典的情况
            if isinstance(value, dict):
                value = value.get("value", "")

            # 确保value是字符串
            if not isinstance(value, str):
                value = str(value) if value is not None else ""

            # 检查值是否为空
            if not value or not value.strip():
                logger.debug(f"跳过空值商品规格: {field_title}")
                return True  # 空值视为成功，但不执行任何操作

            # 根据商品规格编号查找对应的input区域
            spec_number = self._extract_spec_number(field_title)
            if spec_number is None:
                logger.error(f"无法提取规格编号: {field_title}")
                return False

            # 分割多个值
            values = [v.strip() for v in value.split(',') if v.strip()]
            if not values:
                logger.warning(f"没有有效的规格值: {value}")
                return False

            logger.debug(f"需要输入 {len(values)} 个规格值: {values}")

            success_count = 0

            # 为每个值输入到对应的input中
            for i, val in enumerate(values):
                try:
                    # 查找当前可用的input元素
                    input_selector = f"x:.//input[@data-testid='beast-core-input-htmlInput']"
                    current_inputs = container.eles(input_selector, timeout=5)
                    available_inputs = [inp for inp in current_inputs if inp.states.is_displayed]

                    logger.debug(f"处理第 {i+1} 个值 '{val}'，当前有 {len(available_inputs)} 个可用input")

                    # 如果是第一个值，需要找到属于当前规格的第一个空input
                    if i == 0 and available_inputs:
                        target_input = self._find_spec_input(available_inputs, spec_number)
                        if target_input:
                            target_input.clear(True)
                            target_input.input(val)
                            logger.debug(f"成功输入第一个规格值: {val}")

                            # 让input失去焦点
                            try:
                                self.tab.run_js("arguments[0].blur();", target_input)
                                logger.debug(f"input元素已失去焦点: {val}")
                            except Exception as e:
                                logger.debug(f"blur失败: {e}")

                            success_count += 1
                            self.tab.wait(1)  # 等待页面更新
                        else:
                            logger.warning(f"未找到属于规格{spec_number}的input")

                    # 如果是后续值，需要先添加新的子规格input
                    elif i > 0:
                        # 查找"继续添加"或"添加子规格"按钮
                        add_sub_spec_success = self._add_sub_spec_input(container, field_title)

                        if add_sub_spec_success:
                            # 重新查找input元素
                            self.tab.wait(1)
                            new_inputs = container.eles(input_selector, timeout=5)
                            new_available_inputs = [inp for inp in new_inputs if inp.states.is_displayed]

                            # 使用最后一个（新添加的）input
                            if len(new_available_inputs) > len(available_inputs):
                                last_input = new_available_inputs[-1]
                                last_input.clear()
                                last_input.input(val)
                                logger.debug(f"成功输入规格值: {val} 到新添加的input")

                                # 让input失去焦点
                                try:
                                    self.tab.run_js("arguments[0].blur();", last_input)
                                    logger.debug(f"新添加的input元素已失去焦点: {val}")
                                except Exception as e:
                                    logger.debug(f"blur失败: {e}")

                                success_count += 1
                                self.tab.wait(1)
                            else:
                                logger.warning(f"添加子规格后没有找到新的input: {val}")
                        else:
                            logger.warning(f"无法添加子规格input，跳过值: {val}")

                except Exception as e:
                    logger.error(f"输入规格值失败: {val} - {str(e)}")

            return success_count > 0

        except Exception as e:
            logger.error(f"处理商品规格输入失败: {str(e)}")
            return False

    @staticmethod
    def _find_spec_input(available_inputs: list, spec_number: int):
        """
        找到属于指定规格的input元素

        Args:
            available_inputs: 所有可用的input元素列表
            spec_number: 规格编号（1或2）

        Returns:
            属于指定规格的第一个空input元素，如果没找到则返回None
        """
        try:
            # 策略：根据规格编号和input的位置来判断
            # 规格1通常使用前面的input，规格2使用后面的input

            # 首先找到所有空的input
            empty_inputs = []
            for inp in available_inputs:
                try:
                    value = inp.value
                    if not value or value.strip() == "" or value == "请输入":
                        empty_inputs.append(inp)
                except:
                    empty_inputs.append(inp)  # 如果无法获取值，也认为是空的

            logger.debug(f"找到 {len(empty_inputs)} 个空input，总共 {len(available_inputs)} 个input")

            if not empty_inputs:
                return None

            # 根据规格编号选择合适的input
            if spec_number == 1:
                # 规格1：选择第一个空input
                return empty_inputs[0]
            elif spec_number == 2:
                # 规格2：选择最后一个空input（通常是新添加的规格对应的input）
                return empty_inputs[-1]
            else:
                # 其他情况，返回第一个空input
                return empty_inputs[0]

        except Exception as e:
            logger.error(f"查找规格{spec_number}的input失败: {str(e)}")
            # 如果出错，返回第一个可用input作为fallback
            return available_inputs[0] if available_inputs else None

    def _add_parent_spec(self, container, count: int) -> bool:
        """
        添加父规格（点击动态的"添加父规格 X"按钮）

        Args:
            container: product-skc容器
            count: 需要添加的父规格数量

        Returns:
            是否成功添加
        """
        try:
            success_count = 0

            # 逐个添加父规格
            for i in range(count):
                # 每次都重新查找按钮，因为按钮文本是动态的
                current_selects = len(container.eles("x:.//div[@data-testid='beast-core-select']", timeout=2))
                expected_next_number = current_selects + 1

                # 查找动态的添加父规格按钮
                dynamic_selectors = [
                    f"x://button[contains(.,'添加父规格 {expected_next_number}')]",
                ]

                button_found = False
                for selector in dynamic_selectors:
                    add_buttons = container.eles(selector, timeout=2)
                    for button in add_buttons:
                        if button.states.is_displayed:
                            button_text = button.text.strip()
                            # 确保不是子规格按钮
                            if "子规格" not in button_text and "继续添加" not in button_text:
                                logger.debug(f"找到添加父规格按钮: '{button_text}'")

                                # 记录点击前的select数量
                                before_count = len(container.eles("x:.//div[@data-testid='beast-core-select']", timeout=2))

                                # 点击按钮
                                self.tab.run_js("arguments[0].click();", button)
                                self.tab.wait(2)  # 等待页面更新

                                # 检查是否成功添加
                                after_count = len(container.eles("x:.//div[@data-testid='beast-core-select']", timeout=2))

                                if after_count > before_count:
                                    logger.debug(f"成功添加父规格 {expected_next_number}: {before_count} -> {after_count}")
                                    success_count += 1
                                    button_found = True
                                    break
                                else:
                                    logger.warning(f"点击按钮后没有增加select元素: {before_count} -> {after_count}")

                    if button_found:
                        break

                if not button_found:
                    logger.warning(f"未找到添加父规格 {expected_next_number} 的按钮")
                    break

            logger.debug(f"成功添加 {success_count} 个父规格，期望添加 {count} 个")
            return success_count > 0

        except Exception as e:
            logger.error(f"添加父规格失败: {str(e)}")
            return False

    def _add_sub_spec_input(self, container, field_title: str = "") -> bool:
        """
        添加子规格input（点击"继续添加子规格"按钮）

        Args:
            container: product-skc容器
            field_title: 当前字段标题，用于确定规格编号

        Returns:
            是否成功添加
        """
        try:
            # 从字段标题中提取规格编号
            spec_number = self._extract_spec_number(field_title)
            logger.debug(f"为规格{spec_number}查找添加子规格按钮")

            # 查找可能的添加子规格按钮（更精确的选择器）
            add_sub_selectors = [
                "x://button[contains(.,'继续添加子规格')]",
            ]

            # 首先尝试找到所有可能的按钮
            all_buttons = []
            for selector in add_sub_selectors:
                buttons = container.eles(selector, timeout=2)
                for button in buttons:
                    if button.states.is_displayed:
                        button_text = button.text.strip()
                        # 确保不是添加父规格的按钮
                        if "父规格" not in button_text:
                            all_buttons.append(button)

            logger.debug(f"找到 {len(all_buttons)} 个可能的添加子规格按钮")

            # 如果只有一个按钮，直接使用
            if len(all_buttons) == 1:
                button = all_buttons[0]
                button_text = button.text.strip()
                logger.debug(f"只有一个按钮，直接点击: '{button_text}'")
                self.tab.run_js("arguments[0].click();", button)
                self.tab.wait(1)
                return True

            # 如果有多个按钮，需要找到正确的那个
            elif len(all_buttons) > 1:
                # 策略：找到最后一个可见的按钮（通常是最新添加的规格对应的按钮）
                # 或者根据按钮在页面中的位置来判断

                # 先尝试找到位置最靠下的按钮（最后添加的规格）
                if spec_number > 1:
                    # 对于第二个规格，选择最后一个按钮
                    button = all_buttons[-1]
                else:
                    # 对于第一个规格，选择第一个按钮
                    button = all_buttons[0]

                button_text = button.text.strip()
                logger.debug(f"多个按钮中选择: '{button_text}' (规格{spec_number})")
                self.tab.run_js("arguments[0].click();", button)
                self.tab.wait(1)
                return True

            logger.warning("未找到可用的添加子规格按钮")
            return False

        except Exception as e:
            logger.error(f"添加子规格input失败: {str(e)}")
            return False

    @staticmethod
    def _extract_spec_number(field_title: str) -> int | None:
        """
        从字段标题中提取规格编号

        Args:
            field_title: 字段标题（如"父规格1"、"商品规格2"）

        Returns:
            规格编号（1-5）或None
        """
        try:
            # 提取数字
            import re
            match = re.search(r'(\d+)', field_title)
            if match:
                return int(match.group(1))
            return None
        except Exception:
            return None

