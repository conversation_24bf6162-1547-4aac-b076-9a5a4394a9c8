"""
SKU预览图组件综合测试

测试SKU预览图组件的功能：
1. 查找素材中心按钮
2. 打开素材中心弹窗
3. 选择预览图
4. 确认选择并关闭弹窗

先填充ProductSKC数据，然后测试SKU预览图功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from biz.temu import Temu
from components.product_skc import ProductSkcComponent
from components import ComponentFactory


def fill_product_skc_data(tab):
    """填充ProductSKC数据"""
    
    print("\n🔧 创建ProductSkcComponent实例...")
    product_skc_component = ProductSkcComponent(tab)
    
    # ProductSKC测试数据
    test_cases = [
        ("父规格1", {"value": "颜色", "require": 0}),
        ("商品规格1", {"value": "红色", "require": 0}),
    ]
    
    print("\n📝 开始填充ProductSKC数据...")
    for field_title, data in test_cases:
        print(f"\n🎯 填充: {field_title} = {data['value']}")
        
        result = product_skc_component.fill_data(field_title, data)
        
        if result.success:
            print(f"✅ {field_title} 成功: {result.message}")
        else:
            print(f"❌ {field_title} 失败: {result.message}")
        
        tab.wait(3)
    
    print(f"\n🎉 ProductSKC数据填充完成！")
    
    # 等待SKC数据处理完成，让页面更新
    print("⏳ 等待SKC数据处理完成...")
    tab.wait(5)
    
    # 点击页面空白区域确保数据保存
    print("💾 确保SKC数据保存...")
    try:
        tab.run_js("document.body.click();")
        tab.wait(3)
    except Exception as e:
        print(f"点击页面异常: {e}")
    
    print("✅ SKC数据处理完成，SKU字段应该已解锁")
    return True


def test_sku_preview_component():
    """测试SKU预览图组件"""
    
    print("🎯 测试SKU预览图组件")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 先填充ProductSKC数据
        if not fill_product_skc_data(tab):
            print("❌ ProductSKC数据填充失败")
            return False
        
        # 4. 测试SKU预览图组件
        print(f"\n📝 开始测试SKU预览图组件...")
        
        # 创建SKU预览图组件
        sku_preview_component = ComponentFactory.create_component("sku_preview", tab)
        if not sku_preview_component:
            print("❌ 无法创建sku_preview组件")
            return False
        
        # 测试数据 - 使用一个示例图片名称
        test_data = {
            "title": "预览图",
            "type": "sku_preview",
            "require": 1,
            "value": "/Users/<USER>/Desktop/temu/马桶贴/HZ-A138/HZ-A138-1.png"
        }
        
        field_title = test_data["title"]
        field_data = {
            "value": test_data["value"],
            "require": test_data["require"]
        }
        
        print(f"\n🎯 测试字段: {field_title}")
        print(f"📋 测试数据: {field_data}")
        
        # 执行填充操作
        result = sku_preview_component.fill_data(field_title, field_data)
        
        if result.success:
            print(f"✅ {field_title} 成功: {result.message}")
        else:
            print(f"❌ {field_title} 失败: {result.message}")
        
        print(f"\n🎉 SKU预览图组件测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False


def test_find_sku_preview_elements():
    """测试查找SKU预览图相关元素"""
    
    print("🎯 测试查找SKU预览图相关元素")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 先填充ProductSKC数据
        if not fill_product_skc_data(tab):
            print("❌ ProductSKC数据填充失败")
            return False
        
        # 4. 测试查找SKU预览图元素
        print("\n🔍 测试查找SKU预览图元素...")
        
        # 创建SKU预览图组件
        sku_preview_component = ComponentFactory.create_component("sku_preview", tab)
        
        field_title = "预览图"
        print(f"\n🎯 查找字段: {field_title}")
        
        # 测试JavaScript XPath
        js_xpath = f"count(//th[.//*[normalize-space(text())='{field_title}']]/preceding-sibling::th) + 1"
        column_count = sku_preview_component._execute_js_xpath(js_xpath)
        print(f"📊 JavaScript XPath结果: {column_count}")
        
        # 查找字段容器
        container = sku_preview_component.find_field_container(field_title)
        
        if container:
            print(f"✅ 成功找到 {field_title} 容器")
            print(f"📋 容器标签: {container.tag}")
            print(f"📋 容器文本: {container.text}")
            print(f"📋 容器属性: {container.attrs}")
        else:
            print(f"❌ 未找到 {field_title} 容器")
            
            # 尝试查找所有包含"素材中心"的元素
            print("\n🔍 查找所有包含'素材中心'的元素...")
            material_elements = tab.eles("x://*[contains(text(), '素材中心')]")
            print(f"📋 找到 {len(material_elements)} 个包含'素材中心'的元素")
            
            for i, element in enumerate(material_elements[:3]):  # 只显示前3个
                print(f"   元素 {i+1}: {element.tag} - {element.text[:50]}...")
                print(f"   属性: {element.attrs}")
            
            # 尝试查找所有包含"预览图"的元素
            print("\n🔍 查找所有包含'预览图'的元素...")
            preview_elements = tab.eles("x://*[contains(text(), '预览图')]")
            print(f"📋 找到 {len(preview_elements)} 个包含'预览图'的元素")
            
            for i, element in enumerate(preview_elements[:3]):  # 只显示前3个
                print(f"   元素 {i+1}: {element.tag} - {element.text[:50]}...")
                print(f"   属性: {element.attrs}")
        
        print(f"\n🎉 SKU预览图元素查找测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False


def test_material_center_popup():
    """测试素材中心弹窗功能"""
    
    print("🎯 测试素材中心弹窗功能")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 先填充ProductSKC数据
        if not fill_product_skc_data(tab):
            print("❌ ProductSKC数据填充失败")
            return False
        
        # 4. 测试素材中心弹窗
        print("\n🔍 测试素材中心弹窗功能...")
        
        # 创建SKU预览图组件
        sku_preview_component = ComponentFactory.create_component("sku_preview", tab)
        
        # 查找素材中心按钮
        field_title = "预览图"
        container = sku_preview_component.find_field_container(field_title)

        if container:
            print(f"✅ 找到素材中心按钮")

            # 点击素材中心按钮
            print("🖱️ 点击素材中心按钮...")
            container.click()
            tab.wait(3)

            # 检查弹窗是否打开
            is_opened = sku_preview_component.is_opened()
            if is_opened:
                print("✅ 素材中心弹窗成功打开")

                # 查找弹窗中的元素
                print("🔍 查找弹窗中的图片元素...")
                image_containers = tab.eles("x://div[contains(@class, 'index-module_cardContainer')]")
                print(f"📋 找到 {len(image_containers)} 个图片容器")

                if image_containers:
                    for i, img_container in enumerate(image_containers[:3]):  # 只显示前3个
                        img_text = img_container.text.strip()[:30]
                        print(f"   图片 {i+1}: {img_text}...")

                # 尝试关闭弹窗
                print("❌ 尝试关闭弹窗...")
                close_btn = tab.ele("x://button[contains(@class, 'close') or @aria-label='Close']", timeout=3)
                if close_btn:
                    close_btn.click()
                    tab.wait(2)
                    print("✅ 弹窗已关闭")
                else:
                    print("⚠️ 未找到关闭按钮，弹窗可能仍然打开")
            else:
                print("❌ 素材中心弹窗未能打开")
        else:
            print("❌ 未找到素材中心按钮")
        
        print(f"\n🎉 素材中心弹窗测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False


if __name__ == "__main__":
    print("🚀 开始测试SKU预览图组件...")
    
    # # 首先测试元素查找
    # print("\n=== 测试元素查找 ===")
    # test_find_sku_preview_elements()
    #
    # # 然后测试弹窗功能
    # print("\n=== 测试弹窗功能 ===")
    # test_material_center_popup()

    # 最后测试完整功能
    print("\n=== 测试完整功能 ===")
    test_sku_preview_component()
    
    print("\n🎉 所有测试完成！")
