"""
下拉输入组件

处理可输入的下拉选择字段（combobox类型）
"""

from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class SelectInputComponent(BaseComponent):
    """下拉输入组件"""

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充下拉输入字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据

        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            # 点击打开下拉菜单
            self.tab.run_js("arguments[0].click();", field_container)
            self.tab.wait(3)

            value = field_data.get("value")
            field_container.clear(True)
            field_container.input(value)

            # 值输入后选中
            self.tab.wait(1)
            self.tab.ele(f"x://ul[@role='listbox']//span[contains(.,{value}) and contains(@class, 'name-label_name')]", timeout=6).click()

            # 校验结果是否正确
            input_value = field_container.value
            if input_value == value:
                return success_response(f"{field_title} 选择完成 {value}")

            return error_response(f"{field_title} 选择失败 {value}")
        except Exception as e:
            error_msg = f"填充下拉输入字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)


    def find_field_container(self, field_title: str):
        """
        查找字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            return self.tab.ele(f"x://div[@data-testid='beast-core-form-item' and .//span[contains(., '{field_title}')] and contains(@id, 'productProperties')]//input", timeout=5)
        except Exception as e:
            logger.error(f"查找字段容器失败: {field_title} -> {str(e)}")
            return None

