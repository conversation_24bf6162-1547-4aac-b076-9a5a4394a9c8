"""
数字输入组件

处理数字类型的输入字段
"""

from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class InputNumberComponent(BaseComponent):
    """数字输入组件"""

    def find_field_container(self, field_title: str):
        """
        查找字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            input_selector = f"x://div[.//div[contains(., '{field_title}')] and @data-testid='beast-core-inputNumber']//input"
            input_element = self.tab.ele(input_selector, timeout=5)
            if input_element:
                return input_element

            logger.warning(f"未找到字段容器: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找字段容器失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充数字输入字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据

        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")
            
            # 验证数字格式
            try:
                numeric_value = float(value)
            except (ValueError, TypeError):
                error_msg = f"字段值不是有效数字: {value}"
                self.log_operation(field_title, "验证数字格式", False, error_msg)
                return error_response(error_msg)
            

            input_ele = self.find_field_container(field_title)
            if not input_ele:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            # 清空并输入新值
            input_ele.clear(True)
            input_ele.input(str(numeric_value))

            self.log_operation(field_title, f"输入数字: {numeric_value}")
            return success_response(f"成功填充字段 '{field_title}': {numeric_value}")
            
        except Exception as e:
            error_msg = f"填充数字输入字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)

