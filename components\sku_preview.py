"""
SKU预览图组件

处理SKU预览图上传字段
"""

import os
from pathlib import Path
from typing import Any, Dict

from loguru import logger
from utils.element_util import try_click
from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class SkuPreviewComponent(BaseComponent):
    """SKU预览图组件"""

    def _execute_js_xpath(self, xpath_expression: str):
        """
        执行JavaScript XPath表达式

        Args:
            xpath_expression: XPath表达式

        Returns:
            执行结果
        """
        try:
            # 使用repr()来安全地传递XPath表达式，避免引号冲突
            js_code = f"""
            var xpath = {repr(xpath_expression)};
            return document.evaluate(
                xpath,
                document,
                null,
                XPathResult.NUMBER_TYPE,
                null
            ).numberValue;
            """
            result = self.tab.run_js(js_code)
            logger.debug(f"JavaScript XPath执行结果: {xpath_expression} -> {result}")
            return result
        except Exception as e:
            logger.error(f"JavaScript XPath执行失败: {xpath_expression} - {str(e)}")
            return None

    def find_field_container(self, field_title: str):
        """
        查找表格内的素材中心按钮

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            js_xpath = f"count(//th[.//*[normalize-space(text())='{field_title}']]/preceding-sibling::th) + 1"
            column_count = self._execute_js_xpath(js_xpath)

            if column_count and column_count > 0:
                column_index = int(column_count)
                logger.debug(f"找到字段列索引: {field_title} -> 第{column_index}列")

                # 根据列索引查找对应的输入框
                table_selector = f"x://table[.//*[contains(normalize-space(text()), '{field_title}')]]//tbody//td[{column_index}]//div[.='素材中心']"
                element = self.tab.ele(table_selector, timeout=3)
                if element:
                    logger.debug(f"找到表格素材中心按钮(包含匹配): {field_title}")
                    return element

            logger.warning(f"未找到字段容器: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找字段容器失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充SKU预览图字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")
            
            # 验证图片文件
            image_path = self._validate_image_path(value)
            if not image_path:
                error_msg = f"无效的图片文件路径: {value}"
                self.log_operation(field_title, "验证图片路径", False, error_msg)
                return error_response(error_msg)
            
            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            for _ in range(3):
                try:
                    field_container.click()
                    is_success = self.is_opened()
                    if not is_success:
                        logger.warning(f"素材中心弹窗未打开")
                        continue
                    break
                except Exception as e:
                    logger.error(f"点击元素失败: {e}")
            else:
                error_msg = "尝试 3 次后素材中心弹窗仍未打开"
                logger.error(error_msg)
                return error_response(error_msg)

            image_name = os.path.basename(image_path).split('.')[0]
            success = self._select_preview_image(image_name)
            if not success:
                error_msg = f"预览图上传失败"
                self.log_operation(field_title, "上传预览图", False, error_msg)
                return error_response(error_msg)

            self.log_operation(field_title, f"上传预览图: {os.path.basename(image_path)}")
            return success_response(f"成功上传预览图到字段 '{field_title}': {os.path.basename(image_path)}")
            
        except Exception as e:
            error_msg = f"填充SKU预览图字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)
    
    def _validate_image_path(self, value) -> str | None:
        """
        验证图片文件路径
        
        Args:
            value: 图片路径值
            
        Returns:
            有效的图片文件路径或None
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        
        try:
            if isinstance(value, str):
                path = Path(value)
                if path.is_file() and path.suffix.lower() in image_extensions:
                    return str(path)
            elif hasattr(value, '__fspath__'):
                path = Path(value)
                if path.is_file() and path.suffix.lower() in image_extensions:
                    return str(path)
        except Exception:
            pass
        
        return None

    def is_opened(self) -> bool:
        """
        判断是否已打开

        Returns:
            bool: 是否已打开
        """
        try:
            ele = self.tab.ele("x://span[.='进入素材中心']", timeout=3)
            if not ele:
                return False

            logger.success("素材中心弹窗已打开")
            return True

        except Exception as e:
            logger.error(e)
            return False

    def _select_preview_image(self, image_name: str) -> bool:
        """
        选择图片预览图
        
        Args:
            image_name: 图片名

        Returns:
            是否选择成功
        """
        try:

            for _ in range(3):
                container = self.tab.ele(
                    f"x://div[contains(@class, 'index-module_cardContainer') and .//div[contains(text(), '{image_name}')]]",
                    timeout=5)
                if not container:
                    logger.warning(f"未找到图片容器: {image_name}")
                    continue

                container.click()
                self.tab.wait(0.5)
                logger.info("正在确认选择...")
                result = try_click(self.tab,"x://button[.='确认' and @data-testid='beast-core-button']")
                if not result:
                    continue
                self.tab.wait(2)
                modal_still_open = self.is_opened()
                if modal_still_open:
                    logger.warning("素材中心弹窗可能仍然打开")
                else:
                    logger.success("素材中心弹窗已关闭")
                return True

            return False

        except Exception as e:
            logger.error(f"上传预览图失败: {str(e)}")
            return False
