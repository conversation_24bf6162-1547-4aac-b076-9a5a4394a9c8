"""
组件工厂

根据字段类型动态创建对应的UI组件
"""

from typing import Dict, Type, Optional

from DrissionPage._pages.chromium_tab import ChromiumTab
from loguru import logger

from .base_component import BaseComponent


class ComponentFactory:
    """UI组件工厂类"""
    
    # 组件类型映射表
    _component_map: Dict[str, Type[BaseComponent]] = {}
    
    @classmethod
    def register_component(cls, component_type: str, component_class: Type[BaseComponent]):
        """
        注册组件类型
        
        Args:
            component_type: 组件类型名称
            component_class: 组件类
        """
        cls._component_map[component_type] = component_class
        # logger.debug(f"注册组件类型: {component_type} -> {component_class.__name__}")
    
    @classmethod
    def create_component(cls, component_type: str, tab: ChromiumTab) -> Optional[BaseComponent]:
        """
        创建组件实例
        
        Args:
            component_type: 组件类型
            tab: 浏览器标签页对象
            
        Returns:
            组件实例或None
        """
        component_class = cls._component_map.get(component_type)
        if component_class:
            logger.debug(f"创建组件: {component_type}")
            return component_class(tab)
        else:
            logger.warning(f"未找到组件类型: {component_type}")
            return None
    
    @classmethod
    def get_supported_types(cls) -> list[str]:
        """
        获取支持的组件类型列表
        
        Returns:
            支持的组件类型列表
        """
        return list(cls._component_map.keys())
    
    @classmethod
    def is_supported(cls, component_type: str) -> bool:
        """
        检查是否支持指定的组件类型
        
        Args:
            component_type: 组件类型
            
        Returns:
            是否支持
        """
        return component_type in cls._component_map


# 延迟导入组件类以避免循环导入
def _register_all_components():
    """注册所有组件类型"""
    try:
        from .textarea import TextareaComponent
        ComponentFactory.register_component("textarea", TextareaComponent)
    except ImportError:
        logger.warning("TextareaComponent 导入失败")
    
    try:
        from .select_input import SelectInputComponent
        ComponentFactory.register_component("select_input", SelectInputComponent)
    except ImportError:
        logger.warning("SelectInputComponent 导入失败")
    
    try:
        from .radio import RadioComponent
        ComponentFactory.register_component("radio", RadioComponent)
    except ImportError:
        logger.warning("RadioComponent 导入失败")
    
    try:
        from .input_number import InputNumberComponent
        ComponentFactory.register_component("input_number", InputNumberComponent)
    except ImportError:
        logger.warning("InputNumberComponent 导入失败")
    
    try:
        from .upload_input import UploadInputComponent
        ComponentFactory.register_component("upload_input", UploadInputComponent)
    except ImportError:
        logger.warning("UploadInputComponent 导入失败")
    
    try:
        from .select import SelectComponent
        ComponentFactory.register_component("select", SelectComponent)
    except ImportError:
        logger.warning("SelectComponent 导入失败")
    
    try:
        from .input import InputComponent
        ComponentFactory.register_component("input", InputComponent)
    except ImportError:
        logger.warning("InputComponent 导入失败")

    try:
        from .input_in_table import InputInTableComponent
        ComponentFactory.register_component("input_in_table", InputInTableComponent)
    except ImportError:
        logger.warning("InputInTableComponent 导入失败")

    try:
        from .grid_wrapper import GridWrapperComponent
        ComponentFactory.register_component("grid_wrapper", GridWrapperComponent)
    except ImportError:
        logger.warning("GridWrapperComponent 导入失败")
    
    try:
        from .sku_preview import SkuPreviewComponent
        ComponentFactory.register_component("sku_preview", SkuPreviewComponent)
    except ImportError:
        logger.warning("SkuPreviewComponent 导入失败")
    
    try:
        from .decoration import DecorationComponent
        ComponentFactory.register_component("decoration", DecorationComponent)
    except ImportError:
        logger.warning("DecorationComponent 导入失败")

    try:
        from .product_skc import ProductSkcComponent
        ComponentFactory.register_component("product_skc_select", ProductSkcComponent)
        ComponentFactory.register_component("product_skc_input", ProductSkcComponent)
    except ImportError:
        logger.warning("ProductSkcComponent 导入失败")

    try:
        from .product_specification import ProductSpecificationComponent
        ComponentFactory.register_component("product_specification", ProductSpecificationComponent)
    except ImportError:
        logger.warning("ProductSpecificationComponent 导入失败")

    try:
        from .dropdown_select import DropdownSelectComponent
        ComponentFactory.register_component("dropdown_select", DropdownSelectComponent)
    except ImportError:
        logger.warning("DropdownSelectComponent 导入失败")


# 自动注册所有组件
_register_all_components()
