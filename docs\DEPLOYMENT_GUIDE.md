# Temu自动化工具 - 部署指南

## 📦 部署包内容

```
TemuAutoTool_Release/
├── TemuAutoTool.exe          # 主程序（包含所有Python依赖）
├── data/
│   └── users.csv.copy        # 用户数据模板
├── config/
│   └── fileds.json          # 字段配置文件（必需）
├── logs/                     # 日志目录（自动创建）
├── 系统要求.txt              # 系统要求说明
├── 使用说明.txt              # 使用说明
├── 常见问题.txt              # 故障排除指南
└── README.md                 # 项目说明文档
```

## 🖥️ 目标电脑系统要求

### 必需组件
- **操作系统**：Windows 10/11（推荐）或 Windows 7/8.1
- **架构**：64位系统
- **Chrome浏览器**：最新版本（用于网页自动化）

### 可选组件（通常已预装）
- **Visual C++ 运行库**：2015-2022版本
- **.NET Framework**：4.7.2或更高版本

## 🚀 部署步骤

### 1. 简单部署（推荐）
1. 将整个 `TemuAutoTool_Release` 文件夹复制到目标电脑
2. 确保目标电脑已安装Chrome浏览器
3. 双击 `TemuAutoTool.exe` 运行

### 2. 兼容性测试
在不同的Windows电脑上测试：
```bash
# 测试命令
TemuAutoTool.exe --help
```

## ⚠️ 常见问题及解决方案

### 问题1：程序无法启动
**可能原因**：缺少Visual C++运行库
**解决方案**：
1. 下载并安装 Microsoft Visual C++ Redistributable
2. 链接：https://aka.ms/vs/17/release/vc_redist.x64.exe

### 问题2：Chrome相关错误
**可能原因**：未安装Chrome浏览器或版本过旧
**解决方案**：
1. 安装最新版Chrome浏览器
2. 确保Chrome在系统PATH中或默认安装位置

### 问题3：防病毒软件误报
**可能原因**：PyInstaller打包的exe被误识别
**解决方案**：
1. 将程序添加到防病毒软件白名单
2. 临时关闭实时保护进行测试

### 问题4：首次运行缓慢
**原因**：单文件exe需要解压到临时目录
**说明**：这是正常现象，后续运行会更快

### 问题5：配置文件加载失败
**可能原因**：config/fileds.json文件缺失或损坏
**解决方案**：
1. 确保config目录存在且包含fileds.json文件
2. 检查文件权限，确保程序可以读取
3. 重新从源码复制配置文件

### 问题6：详情页装修未打开
**可能原因**：网页加载缓慢或元素定位失败
**解决方案**：
1. 检查网络连接是否稳定
2. 确保Chrome浏览器版本最新
3. 尝试关闭其他占用资源的程序
4. 重启程序重试

### 问题7：图片上传重复
**原因**：已修复的已知问题
**说明**：新版本已解决Windows文件系统大小写不敏感导致的重复上传问题

## 🔧 高级部署选项

### 选项1：创建安装程序
使用NSIS或Inno Setup创建专业的安装程序：
```
- 自动检测系统要求
- 自动安装依赖组件
- 创建桌面快捷方式
- 支持卸载
```

### 选项2：便携版部署
```
TemuAutoTool_Portable/
├── TemuAutoTool.exe
├── portable.ini          # 标记为便携版
├── data/
├── config/
└── logs/                 # 日志目录
```

## 📊 兼容性矩阵

| Windows版本 | 兼容性 | 备注 |
|------------|--------|------|
| Windows 11 | ✅ 完全兼容 | 推荐 |
| Windows 10 | ✅ 完全兼容 | 推荐 |
| Windows 8.1 | ⚠️ 基本兼容 | 需要更新运行库 |
| Windows 7 | ⚠️ 有限兼容 | 需要SP1和更新 |

## 🧪 部署前测试清单

### 基础功能测试
- [ ] 在干净的Windows系统上测试
- [ ] 测试不同用户权限（管理员/普通用户）
- [ ] 验证程序正常启动和退出
- [ ] 检查配置文件正确加载

### 浏览器兼容性测试
- [ ] 测试不同Chrome版本（建议最新版）
- [ ] 验证网页自动化功能正常
- [ ] 测试图片上传功能
- [ ] 验证表单填写功能

### 环境兼容性测试
- [ ] 测试网络环境（代理/防火墙）
- [ ] 测试杀毒软件环境
- [ ] 验证在不同分辨率下的显示效果
- [ ] 测试多用户环境下的数据隔离

### 数据处理测试
- [ ] 测试用户数据导入功能
- [ ] 验证Excel文件读取功能
- [ ] 测试商品数据处理流程
- [ ] 验证日志记录功能

### 错误处理测试
- [ ] 测试网络断开时的处理
- [ ] 验证文件缺失时的错误提示
- [ ] 测试异常中断后的恢复能力
- [ ] 验证用户输入验证功能

## 📝 用户使用说明模板

```
🎯 Temu自动化工具使用说明

📋 运行前准备：
1. 确保已安装Chrome浏览器（最新版本）
2. 准备用户数据CSV文件（参考data/users.csv.copy格式）
3. 准备商品数据Excel文件
4. 确保网络连接正常

🚀 启动程序：
1. 双击 TemuAutoTool.exe
2. 首次运行可能需要几秒钟加载时间
3. 按照程序界面提示操作：
   - 选择 [1] 导入用户数据
   - 选择 [2] 选择用户登录
   - 选择 [3] 上传商品数据

📊 使用流程：
1. 导入用户 → 2. 登录账户 → 3. 上传商品 → 4. 查看结果

❓ 遇到问题：
1. 查看程序目录下的logs文件夹中的日志文件
2. 确认Chrome浏览器正常工作
3. 检查config/fileds.json配置文件是否存在
4. 参考常见问题解决方案
5. 联系技术支持
```

## 🔒 安全建议

1. **数字签名**：考虑对exe文件进行数字签名
2. **病毒扫描**：发布前进行全面病毒扫描
3. **完整性校验**：提供文件MD5/SHA256校验值
4. **版本控制**：明确标注版本号和构建日期
5. **数据安全**：用户数据本地存储，不上传到第三方服务器
6. **权限控制**：程序仅需要普通用户权限，无需管理员权限

## 📋 版本更新说明

### v2.1.0 (当前版本)
- ✅ 修复Unicode编码问题，支持表情符号显示
- ✅ 优化Windows环境依赖安装
- ✅ 修复配置文件路径问题
- ✅ 解决重复文件上传问题
- ✅ 优化日志格式，提升可读性
- ✅ 改进虚拟环境检测逻辑
- ✅ 增强错误处理和用户提示

### 已知问题
- 详情页装修功能在某些网络环境下可能需要多次重试
- 大量图片上传时可能需要较长时间

### 计划改进
- 添加批量处理进度显示
- 优化网络超时处理
- 增加更多的错误恢复机制

## 📞 技术支持

### 联系方式
- 项目地址：[GitHub仓库链接]
- 问题反馈：[Issues页面链接]
- 技术文档：docs/目录下的相关文档

### 日志收集
遇到问题时，请提供以下信息：
1. 操作系统版本
2. Chrome浏览器版本
3. 程序版本号
4. logs目录下的最新日志文件
5. 具体的错误截图或描述
