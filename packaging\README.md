# Windows 打包说明

## 🎯 快速打包

### 一键打包（推荐）
在项目根目录运行：
```cmd
packaging\build_windows.bat
```

### 手动打包
```cmd
# 1. 创建虚拟环境
python -m venv venv
venv\Scripts\activate.bat

# 2. 安装依赖
pip install -r packaging\requirements-windows.txt

# 3. 执行打包
pyinstaller packaging\temu_app.spec --clean --noconfirm

# 4. 创建发布包
mkdir release
copy dist\TemuAutoTool.exe release\
mkdir release\data
copy data\users.csv.copy release\data\
mkdir release\config
copy config\* release\config\
```

## 📁 打包文件说明

- **requirements-windows.txt**: Windows 专用依赖包列表
- **temu_app.spec**: PyInstaller 配置文件
- **build_windows.bat**: 自动打包脚本
- **Windows打包准备说明.md**: 详细的修改说明
- **Windows打包检查清单.md**: 完整的检查清单

## 🎉 打包结果

打包完成后，在 `release` 目录中会生成：
- `TemuAutoTool.exe` - 主程序
- `使用说明.txt` - 用户指南
- `data/` - 数据文件目录
- `config/` - 配置文件目录

## 📋 环境要求

- Windows 10/11
- Python 3.8+
- 2GB+ 可用磁盘空间

## 🔧 故障排除

如果打包失败，请检查：
1. Python 版本是否符合要求
2. 网络连接是否正常（下载依赖包）
3. 磁盘空间是否充足
4. 是否有杀毒软件干扰

详细说明请参考 `Windows打包准备说明.md`
