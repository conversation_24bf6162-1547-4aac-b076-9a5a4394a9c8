#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一字典修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from biz.temu import Temu
from components.product_skc import ProductSkcComponent

def test_unified_dict_fix():
    """测试统一字典修复"""
    
    print("🎯 测试统一字典修复")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 创建ProductSKC组件实例
        print("\n🔧 创建ProductSkcComponent实例...")
        product_skc_component = ProductSkcComponent(tab)
        
        # 4. 测试各种复杂的字典结构
        test_cases = [
            # 简单字符串
            ("父规格1", {"value": "风格", "require": 0}),
            
            # 嵌套字典（模拟真实数据结构）
            ("商品规格1", {"value": {"value": "简约,复古"}, "require": 0}),
            
            # 更复杂的嵌套
            ("父规格2", {"value": {"value": "颜色", "type": "select"}, "require": 0}),
            
            # 三层嵌套
            ("商品规格2", {"value": {"value": {"value": "红色,蓝色"}}, "require": 0}),
        ]
        
        for field_title, data in test_cases:
            print(f"\n🎯 测试: {field_title}")
            print(f"   原始数据: {data['value']}")
            print(f"   数据类型: {type(data['value'])}")
            
            result = product_skc_component.fill_data(field_title, data)
            
            if result.success:
                print(f"✅ {field_title} 成功: {result.message}")
            else:
                print(f"❌ {field_title} 失败: {result.message}")
            
            tab.wait(3)
        
        print(f"\n🎉 统一字典修复测试完成！")
        print("页面将保持打开状态供查看...")
        print("按 Ctrl+C 退出程序")
        
        try:
            while True:
                tab.wait(10)
                print("⏳ 页面仍然打开中...")
        except KeyboardInterrupt:
            print("\n👋 程序退出")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False

if __name__ == "__main__":
    test_unified_dict_fix()
