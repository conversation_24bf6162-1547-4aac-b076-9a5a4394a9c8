"""
软件授权管理模块
实现基于机器码的一机一码收费系统
"""
import hashlib
import json
import platform
import subprocess
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Tuple

import requests
from cryptography.fernet import Fernet
from loguru import logger


class MachineCodeGenerator:
    """机器码生成器"""
    
    @staticmethod
    def get_machine_code() -> str:
        """
        生成唯一的机器码
        基于硬件信息生成，确保一台机器一个码
        """
        try:
            # 获取多个硬件标识
            identifiers = []
            
            # 1. CPU ID (跨平台)
            if platform.system() == "Windows":
                cpu_id = subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode().strip()
                identifiers.append(cpu_id.split('\n')[1].strip())
            else:
                # macOS/Linux 使用其他方式
                cpu_info = subprocess.check_output("sysctl -n machdep.cpu.brand_string", shell=True).decode().strip()
                identifiers.append(cpu_info)
            
            # 2. 主板序列号
            if platform.system() == "Windows":
                board_serial = subprocess.check_output("wmic baseboard get serialnumber", shell=True).decode().strip()
                identifiers.append(board_serial.split('\n')[1].strip())
            
            # 3. MAC 地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) for elements in range(0,2*6,2)][::-1])
            identifiers.append(mac)
            
            # 4. 系统UUID
            if platform.system() == "Windows":
                system_uuid = subprocess.check_output("wmic csproduct get uuid", shell=True).decode().strip()
                identifiers.append(system_uuid.split('\n')[1].strip())
            
            # 组合所有标识符并生成哈希
            combined = ''.join(identifiers)
            machine_code = hashlib.sha256(combined.encode()).hexdigest()[:16].upper()
            
            logger.debug(f"生成机器码: {machine_code}")
            return machine_code
            
        except Exception as e:
            logger.error(f"生成机器码失败: {e}")
            # 备用方案：使用系统信息
            fallback = f"{platform.node()}{platform.machine()}{platform.processor()}"
            return hashlib.md5(fallback.encode()).hexdigest()[:16].upper()


class LicenseValidator:
    """授权验证器"""
    
    def __init__(self, server_url: str = "https://your-license-server.com"):
        self.server_url = server_url
        self.license_file = Path.home() / ".temu_cli" / "license.dat"
        self.license_file.parent.mkdir(exist_ok=True)
        
        # 加密密钥（实际使用时应该更安全地管理）
        self.cipher_key = b'your-secret-key-here-32-bytes-long'
        self.cipher = Fernet(Fernet.generate_key())  # 实际应使用固定密钥
    
    def validate_license(self) -> Tuple[bool, str, Optional[datetime]]:
        """
        验证授权
        
        Returns:
            Tuple[bool, str, Optional[datetime]]: (是否有效, 消息, 过期时间)
        """
        try:
            # 1. 检查本地授权文件
            if not self.license_file.exists():
                return False, "未找到授权文件，请先激活", None
            
            # 2. 读取并解密授权信息
            license_data = self._read_license_file()
            if not license_data:
                return False, "授权文件损坏，请重新激活", None
            
            # 3. 验证机器码
            current_machine_code = MachineCodeGenerator.get_machine_code()
            if license_data.get("machine_code") != current_machine_code:
                return False, "授权与当前机器不匹配", None
            
            # 4. 检查过期时间
            expire_time = datetime.fromisoformat(license_data.get("expire_time"))
            if datetime.now() > expire_time:
                return False, f"授权已过期 ({expire_time.strftime('%Y-%m-%d %H:%M:%S')})", expire_time
            
            # 5. 在线验证（可选）
            if self._should_online_verify(license_data):
                online_valid = self._verify_online(license_data)
                if not online_valid:
                    return False, "在线验证失败，请检查网络或联系客服", expire_time
            
            return True, f"授权有效，到期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}", expire_time
            
        except Exception as e:
            logger.error(f"授权验证失败: {e}")
            return False, f"验证过程出错: {str(e)}", None
    
    def activate_license(self, activation_code: str) -> Tuple[bool, str]:
        """
        激活授权
        
        Args:
            activation_code: 激活码
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            machine_code = MachineCodeGenerator.get_machine_code()
            
            # 向服务器请求激活
            response = requests.post(f"{self.server_url}/activate", json={
                "activation_code": activation_code,
                "machine_code": machine_code,
                "platform": platform.system(),
                "version": "1.0.0"
            }, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    # 保存授权信息到本地
                    license_data = {
                        "machine_code": machine_code,
                        "activation_code": activation_code,
                        "expire_time": result.get("expire_time"),
                        "user_id": result.get("user_id"),
                        "last_verify": datetime.now().isoformat()
                    }
                    
                    self._save_license_file(license_data)
                    return True, f"激活成功！到期时间: {result.get('expire_time')}"
                else:
                    return False, result.get("message", "激活失败")
            else:
                return False, f"服务器错误: {response.status_code}"
                
        except requests.RequestException as e:
            return False, f"网络错误: {str(e)}"
        except Exception as e:
            logger.error(f"激活失败: {e}")
            return False, f"激活过程出错: {str(e)}"
    
    def _read_license_file(self) -> Optional[dict]:
        """读取授权文件"""
        try:
            with open(self.license_file, 'rb') as f:
                encrypted_data = f.read()
            
            # 解密数据
            decrypted_data = self.cipher.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())
            
        except Exception as e:
            logger.error(f"读取授权文件失败: {e}")
            return None
    
    def _save_license_file(self, license_data: dict):
        """保存授权文件"""
        try:
            # 加密数据
            json_data = json.dumps(license_data).encode()
            encrypted_data = self.cipher.encrypt(json_data)
            
            with open(self.license_file, 'wb') as f:
                f.write(encrypted_data)
                
        except Exception as e:
            logger.error(f"保存授权文件失败: {e}")
            raise
    
    def _should_online_verify(self, license_data: dict) -> bool:
        """判断是否需要在线验证"""
        try:
            last_verify = datetime.fromisoformat(license_data.get("last_verify", "2000-01-01"))
            # 每3天验证一次
            return datetime.now() - last_verify > timedelta(days=3)
        except:
            return True
    
    def _verify_online(self, license_data: dict) -> bool:
        """在线验证"""
        try:
            response = requests.post(f"{self.server_url}/verify", json={
                "machine_code": license_data.get("machine_code"),
                "activation_code": license_data.get("activation_code"),
                "user_id": license_data.get("user_id")
            }, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("valid"):
                    # 更新最后验证时间
                    license_data["last_verify"] = datetime.now().isoformat()
                    self._save_license_file(license_data)
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"在线验证失败: {e}")
            # 网络问题时允许离线使用
            return True


def check_license_decorator(func):
    """授权检查装饰器"""
    def wrapper(*args, **kwargs):
        validator = LicenseValidator()
        is_valid, message, expire_time = validator.validate_license()
        
        if not is_valid:
            print(f"❌ {message}")
            print("请使用 'temu_cli activate <激活码>' 命令激活软件")
            return
        
        # 检查即将过期（7天内）
        if expire_time and expire_time - datetime.now() < timedelta(days=7):
            days_left = (expire_time - datetime.now()).days
            print(f"⚠️ 授权将在 {days_left} 天后过期，请及时续费")
        
        return func(*args, **kwargs)
    
    return wrapper
