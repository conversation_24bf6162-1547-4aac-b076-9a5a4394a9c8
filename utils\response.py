#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用响应实体类

提供标准化的响应格式，包含状态码、消息、成功标识和数据字段
"""

from dataclasses import dataclass
from typing import Any, Optional, Dict


@dataclass
class ApiResponse:
    """
    通用API响应类

    包含标准的响应字段：code, message, success, data
    """
    code: int
    message: str
    success: bool
    data: Optional[Any] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "code": self.code,
            "message": self.message,
            "success": self.success
        }
        if self.data is not None:
            result["data"] = self.data
        return result

    def to_json(self) -> str:
        """转换为JSON字符串"""
        import json
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

    @classmethod
    def success(cls, message: str = "操作成功", data: Optional[Any] = None,
                code: int = 200) -> 'ApiResponse':
        """创建成功响应"""
        return cls(code=code, message=message, success=True, data=data)

    @classmethod
    def error(cls, message: str, code: int = 500,
              data: Optional[Any] = None) -> 'ApiResponse':
        """创建错误响应"""
        return cls(code=code, message=message, success=False, data=data)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ApiResponse':
        """从字典创建响应对象"""
        return cls(
            code=data.get("code", 500),
            message=data.get("message", "未知错误"),
            success=data.get("success", False),
            data=data.get("data")
        )

    def is_success(self) -> bool:
        """判断是否成功"""
        return self.success

    def is_error(self) -> bool:
        """判断是否错误"""
        return not self.success

    def get_data(self, default: Any = None) -> Any:
        """获取数据，如果为空返回默认值"""
        return self.data if self.data is not None else default

    def __str__(self) -> str:
        """字符串表示"""
        status = "SUCCESS" if self.success else "ERROR"
        return f"ApiResponse({status}, code={self.code}, message='{self.message}')"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"ApiResponse(code={self.code}, message='{self.message}', success={self.success}, data={self.data})"


# 便捷函数
def success_response(message: str = "操作成功", data: Optional[Any] = None,
                    code: int = 200) -> ApiResponse:
    """创建成功响应的便捷函数"""
    return ApiResponse.success(message, data, code)


def error_response(message: str, code: int = 500,
                  data: Optional[Any] = None) -> ApiResponse:
    """创建错误响应的便捷函数"""
    return ApiResponse.error(message, code, data)
