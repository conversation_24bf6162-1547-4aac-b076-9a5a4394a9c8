"""
路径辅助工具，用于处理 PyInstaller 打包后的路径问题
"""
import sys
import os
from pathlib import Path


def get_resource_path(relative_path: str) -> str:
    """
    获取资源文件的绝对路径，兼容开发环境和 PyInstaller 打包环境
    
    Args:
        relative_path: 相对路径
        
    Returns:
        str: 绝对路径
    """
    try:
        # PyInstaller 创建临时文件夹，并将路径存储在 _MEIPASS 中
        base_path = sys._MEIPASS
    except AttributeError:
        # 开发环境中使用当前文件的目录
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    return os.path.join(base_path, relative_path)


def get_config_path() -> Path:
    """获取配置文件目录路径"""
    return Path(get_resource_path("config"))


def get_data_path() -> Path:
    """获取数据文件目录路径"""
    return Path(get_resource_path("data"))
