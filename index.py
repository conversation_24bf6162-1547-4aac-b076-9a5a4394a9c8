import json
import os
import warnings
from datetime import datetime
from pathlib import Path
from typing import Any

import click
from loguru import logger

from biz.temu import Temu
from utils.excel import Excel
from utils.user_manager import UserManager
from utils.logger_config import LoggerConfig
from utils.goods_processor import (
    load_fields_config,
    scan_subfolders,
    find_xlsx_files,
    process_excel_row_data
)

# 忽略 openpyxl 的默认样式警告
warnings.filterwarnings("ignore", category=UserWarning, module="openpyxl.styles.stylesheet")

class DataUtil:
    """数据处理工具类，集成Excel状态管理功能"""

    STATUS_COLUMNS = {
        "处理状态": "",  # 成功/失败/处理中
        "处理结果": "",  # 详细的成功消息或失败原因
        "处理时间": ""   # 处理时间戳
    }

    def __init__(self, excel_name: str):
        self.excel_name = excel_name
        # 生成结果文件路径（使用时间戳命名，放在上级目录的result文件夹下）
        from pathlib import Path
        from datetime import datetime
        original_path = Path(excel_name)
        result_dir = original_path.parent.parent / "result"
        result_dir.mkdir(exist_ok=True)  # 确保result目录存在

        # 使用时间戳生成唯一的结果文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.result_file = str(result_dir / f"{original_path.stem}_result_{timestamp}{original_path.suffix}")

        self.excel = Excel(self.excel_name)
        # 自动初始化状态列
        self.ensure_status_columns()

    def read(self):
        """
        读取数据，优先从结果文件读取（如果存在），否则从原文件读取

        Returns:
            list: 数据列表
        """
        from pathlib import Path
        if Path(self.result_file).exists():
            # 如果结果文件存在，从结果文件读取（包含处理状态）
            result_excel = Excel(self.result_file)
            return result_excel.load()
        else:
            # 否则从原文件读取
            return self.excel.load()

    def ensure_status_columns(self) -> bool:
        """
        创建结果文件并添加状态列

        Returns:
            bool: 操作是否成功
        """
        try:
            # 复制原文件到结果文件（每次运行都创建新的结果文件）
            import shutil
            shutil.copy2(self.excel_name, self.result_file)
            logger.debug(f"创建新的结果文件: {self.result_file}")

            # 在结果文件上添加状态列
            result_excel = Excel(self.result_file)
            success = result_excel.ensure_columns(self.STATUS_COLUMNS)
            if success:
                logger.debug(f"结果文件 {self.result_file} 状态列初始化完成")
            else:
                logger.error(f"添加状态列失败: {self.result_file}")
            return success
        except Exception as e:
            logger.error(f"创建结果文件失败: {self.result_file} - {str(e)}")
            return False

    def update_status(self, row_index: int, status: str, message: str = ""):
        """
        更新Excel中指定行的处理状态

        Args:
            row_index: 行索引（从0开始）
            status: 状态（"成功" 或 "失败"）
            message: 状态消息（成功消息或失败原因）
        """
        try:
            updated_values = {
                "处理状态": status,
                "处理结果": message,
                "处理时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            # 在结果文件上更新状态
            result_excel = Excel(self.result_file)
            result_excel.update_by_index(row_index, updated_values)
            logger.info(f"已更新第 {row_index + 1} 行状态到结果文件: {status} - {message}")
        except Exception as e:
            logger.error(f"更新Excel状态失败: {str(e)}")

    def is_already_processed(self, row_data: dict) -> bool:
        """
        检查该行数据是否已经成功处理过

        Args:
            row_data: 行数据

        Returns:
            bool: 如果已经成功处理返回True，否则返回False
        """
        try:
            status = row_data.get("处理状态", "")
            return status == "成功"
        except Exception as e:
            logger.error(f"检查处理状态失败: {str(e)}")
            return False

    def get_processing_statistics(self) -> dict:
        """
        获取处理统计信息

        Returns:
            dict: 统计信息
        """
        try:
            # 从结果文件获取列统计
            result_excel = Excel(self.result_file)
            stats = result_excel.get_column_statistics("处理状态")

            if "error" in stats:
                return {"error": stats["error"]}

            # 计算统计信息
            total_rows = stats["total_rows"]
            value_counts = stats["value_counts"]
            success_count = value_counts.get("成功", 0)
            failed_count = value_counts.get("失败", 0)
            pending_count = total_rows - success_count - failed_count

            return {
                "total_rows": total_rows,
                "success_count": success_count,
                "failed_count": failed_count,
                "pending_count": pending_count,
                "success_rate": round(success_count / total_rows * 100, 2) if total_rows > 0 else 0,
                "status_details": value_counts
            }

        except Exception as e:
            return {"error": f"获取统计信息失败: {str(e)}"}

    def write(self, data):
        self.excel.write(data)

    def append(self, data):
        self.excel.append(data)

    def get(self, field_name, field_value):
        return self.excel.get(field_name, field_value)

    def delete(self, field_name, field_value):
        self.excel.delete(field_name, field_value)

    def delete_by_index(self, row_index):
        self.excel.delete_by_index(row_index)

    def update(self, field_name, field_value, updated_values):
        self.excel.update(field_name, field_value, updated_values)

    def update_by_index(self, row_index, updated_values):
        self.excel.update_by_index(row_index, updated_values)


def _login_with_user_manager(index: str, user_manager: UserManager) -> bool:
    """使用用户管理器进行登录"""
    try:
        temu = Temu.from_user_manager(index, user_manager)
        if not temu:
            click.echo(f"❌ 未找到索引为 {index} 的用户")
            return False

        return temu.login()
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        click.echo(f"❌ 登录失败: {str(e)}")
        return False


def _login(index: str):
    """兼容旧版本的登录函数"""
    user_manager = UserManager()
    if not user_manager.load_users():
        click.echo("❌ 加载用户数据失败")
        return False

    return _login_with_user_manager(index, user_manager)


def _process_category_folder(category_folder_path: str, category_name: str, index: str, user_manager: UserManager) -> None:
    """处理单个分类文件夹下的所有xlsx文件"""
    logger.debug(f"正在处理分类: {category_name}")

    # 加载该分类的字段配置
    fulfilled_fields = load_fields_config(category_name)
    if not fulfilled_fields:
        logger.warning(f"未找到分类 '{category_name}' 的配置")
        return

    # 查找该文件夹下的所有xlsx文件
    xlsx_files = find_xlsx_files(category_folder_path)
    if not xlsx_files:
        logger.warning(f"文件夹 '{category_folder_path}' 下未找到xlsx文件")
        return

    # 创建Temu实例并登录一次
    click.echo(f"📱 正在登录...")
    temu = Temu.from_user_manager(index, user_manager)
    if not temu:
        click.echo(f"❌ 未找到索引为 {index} 的用户")
        return

    if not temu.login():
        click.echo(f"❌ 登录失败")
        return
    click.echo(f"✅ 登录成功")

    click.echo(f"📱 正在选择 {category_name} ...")
    result = temu.select_category(category_name)
    if not result.success:
        click.echo(f"❌ 选择 {category_name} 失败")
        return
    click.echo(f"✅ 选择 {category_name} 成功")

    # 处理每个xlsx文件
    for xlsx_file in xlsx_files:
        click.echo(f"📄 正在处理文件: {xlsx_file}")

        # 创建DataUtil实例（会自动初始化状态列）
        data_util = DataUtil(xlsx_file)
        datas = data_util.read()

        # 显示处理统计
        stats = data_util.get_processing_statistics()
        if "error" not in stats:
            click.echo(f"📊 处理统计: 总计{stats['total_rows']}行, "
                      f"成功{stats['success_count']}行, "
                      f"失败{stats['failed_count']}行, "
                      f"待处理{stats['pending_count']}行")

        # 处理每一行Excel数据
        for row_index, data in enumerate(datas):
            actual_row = row_index + 1  # Excel行号从1开始
            logger.info(f"正在处理第 {actual_row} 行数据...")

            # 检查是否已经成功处理过
            if data_util.is_already_processed(data):
                sku_no = data.get("货号", "未知")
                click.echo(f"⏭️ [{sku_no}] 第 {actual_row} 行已成功处理，跳过")
                continue

            try:
                data_json = process_excel_row_data(data, fulfilled_fields, category_folder_path)
                logger.debug(f"处理数据: {data_json}")

                # 创建商品
                success, message = _create_goods(temu, category_name, data_json)

                # 更新Excel状态
                if success:
                    data_util.update_status(row_index, "成功", message)
                else:
                    data_util.update_status(row_index, "失败", message)
                    click.echo(f"❌ 处理中断：第 {actual_row} 行数据处理失败")
                    click.echo(f"   文件: {xlsx_file}")
                    click.echo(f"   分类: {category_name}")
                    click.echo(f"   失败原因: {message}")
                    continue

            except Exception as e:
                error_message = f"处理异常: {str(e)}"
                logger.exception(f"处理第 {actual_row} 行数据时发生异常")
                data_util.update_status(row_index, "失败", error_message)
                click.echo(f"❌ 处理中断：第 {actual_row} 行数据处理异常")
                click.echo(f"   文件: {xlsx_file}")
                click.echo(f"   分类: {category_name}")
                click.echo(f"   异常信息: {error_message}")
                continue  # 中断当前文件的处理


def _create_goods(temu: Temu, category_name: str, goods_data: dict[str, Any]) -> tuple[bool, str]:
    """创建商品

    Args:
        temu: 已登录的Temu实例
        category_name: 分类名称
        goods_data: 商品数据

    Returns:
        tuple[bool, str]: (创建成功状态, 状态消息)
    """
    response = temu.create_goods(goods_data)

    sku_no = goods_data.get("货号").get("value")
    if not response.success:
        error_message = f"创建商品失败: {response.message}"
        if response.data and "missing_field" in response.data:
            error_message += f" - 缺失字段: {response.data['missing_field']}"

        click.echo(f"❌ [{sku_no}] {error_message}")
        return False, error_message

    success_message = "商品创建成功"
    click.echo(f"✅ [{sku_no}] {success_message}")
    return True, success_message


@click.group()
def cli():
    """Temu 命令行工具"""
    pass


@cli.command()
@click.argument("index", type=str, required=False)
def login(index):
    """用户登录，需提供账号序号"""
    # 初始化用户管理器
    user_manager = UserManager()

    click.echo("📋 正在加载用户数据...")
    if not user_manager.load_users():
        click.echo("❌ 加载用户数据失败，请检查 data/users.csv 文件")
        return

    if user_manager.get_user_count() == 0:
        click.echo("❌ 没有可用的用户数据，请先配置 data/users.csv 文件")
        return

    # 如果没有提供index，让用户选择
    if index is None:
        index = user_manager.prompt_user_selection()
        if index is None:
            return
    else:
        # 验证提供的index是否有效
        if not user_manager.validate_index(index):
            click.echo(f"❌ 无效的账号序号: {index}")
            user_manager.display_users()
            return

    # 执行登录
    user_display_name = user_manager.get_user_display_name(index)
    click.echo(f"📱 正在登录账号: {user_display_name}...")
    if _login_with_user_manager(index, user_manager):
        click.echo(f"✅ 登录成功: {user_display_name}")
    else:
        click.echo(f"❌ 登录失败: {user_display_name}")


@cli.command("cg")
@click.argument("index", type=str, required=False)
@click.argument("base_folder_path", type=str, required=False)
def create_goods(index, base_folder_path):
    """创建商品命令

    Args:
        index: 账号序号
        base_folder_path: 基础文件夹路径，包含按分类命名的子文件夹
    """
    # 初始化用户管理器
    user_manager = UserManager()

    click.echo("📋 正在加载用户数据...")
    if not user_manager.load_users():
        click.echo("❌ 加载用户数据失败，请检查 data/users.csv 文件")
        return

    if user_manager.get_user_count() == 0:
        click.echo("❌ 没有可用的用户数据，请先配置 data/users.csv 文件")
        return

    # 如果没有提供index，让用户选择
    if index is None:
        index = user_manager.prompt_user_selection()
        if index is None:
            return
    else:
        # 验证提供的index是否有效
        if not user_manager.validate_index(index):
            click.echo(f"❌ 无效的账号序号: {index}")
            user_manager.display_users()
            return

    if base_folder_path is None:
        base_folder_path = click.prompt("请输入商品数据文件夹路径", type=str)

    # 检查基础文件夹是否存在
    base_path = Path(base_folder_path)
    if not base_path.exists():
        click.echo(f"错误: 文件夹路径 '{base_folder_path}' 不存在")
        return

    # 扫描所有子文件夹
    subfolders = scan_subfolders(base_folder_path)
    if not subfolders:
        click.echo(f"警告: 文件夹 '{base_folder_path}' 下未找到子文件夹")
        return

    click.echo(f"找到 {len(subfolders)} 个分类文件夹: {', '.join(subfolders)}")

    # 处理每个分类文件夹
    for subfolder in subfolders:
        if "圆柱体包装" in subfolder or "长方体包装" in subfolder:
            continue

        category_folder_path = str(base_path / subfolder)
        _process_category_folder(category_folder_path, subfolder, index, user_manager)


@cli.command()
def view_product():
    click.echo("Viewing products...")


@cli.command("users")
def list_users():
    """显示所有可用用户"""
    user_manager = UserManager()

    click.echo("📋 正在加载用户数据...")
    if not user_manager.load_users():
        click.echo("❌ 加载用户数据失败，请检查 data/users.csv 文件")
        return

    if user_manager.get_user_count() == 0:
        click.echo("❌ 没有可用的用户数据，请先配置 data/users.csv 文件")
        click.echo("\n💡 提示: 请编辑 data/users.csv 文件添加用户信息")
        click.echo("   格式: index,phone,password,username")
        click.echo("   示例: 1,13800138000,your_password,用户1")
        return

    user_manager.display_users()
    click.echo(f"\n📊 总计: {user_manager.get_user_count()} 个用户")


if __name__ == "__main__":
    # # 检查是否有命令行参数
    # import sys
    # if len(sys.argv) == 1:
        # 没有命令行参数，启动菜单界面
        # from app import main as app_main
        #
        # app_main()
    # else:
    #     # 有命令行参数，使用原有的CLI界面
    #     cli()

    # 初始化日志配置
    LoggerConfig.setup_logger()

    # 设置控制台编码以支持Unicode字符
    from app import setup_console_encoding
    setup_console_encoding()

    from app import main as app_main
    app_main()
