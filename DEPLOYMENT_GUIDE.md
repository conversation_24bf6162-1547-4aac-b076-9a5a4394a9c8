# Temu自动化工具 - 部署指南

## 📦 部署包内容

```
TemuAutoTool_Release/
├── TemuAutoTool.exe          # 主程序（包含所有Python依赖）
├── data/
│   └── users.csv.copy        # 用户数据模板
├── config/                   # 配置文件目录
├── 系统要求.txt              # 系统要求说明
├── 使用说明.txt              # 使用说明
└── 常见问题.txt              # 故障排除指南
```

## 🖥️ 目标电脑系统要求

### 必需组件
- **操作系统**：Windows 10/11（推荐）或 Windows 7/8.1
- **架构**：64位系统
- **Chrome浏览器**：最新版本（用于网页自动化）

### 可选组件（通常已预装）
- **Visual C++ 运行库**：2015-2022版本
- **.NET Framework**：4.7.2或更高版本

## 🚀 部署步骤

### 1. 简单部署（推荐）
1. 将整个 `TemuAutoTool_Release` 文件夹复制到目标电脑
2. 确保目标电脑已安装Chrome浏览器
3. 双击 `TemuAutoTool.exe` 运行

### 2. 兼容性测试
在不同的Windows电脑上测试：
```bash
# 测试命令
TemuAutoTool.exe --help
```

## ⚠️ 常见问题及解决方案

### 问题1：程序无法启动
**可能原因**：缺少Visual C++运行库
**解决方案**：
1. 下载并安装 Microsoft Visual C++ Redistributable
2. 链接：https://aka.ms/vs/17/release/vc_redist.x64.exe

### 问题2：Chrome相关错误
**可能原因**：未安装Chrome浏览器或版本过旧
**解决方案**：
1. 安装最新版Chrome浏览器
2. 确保Chrome在系统PATH中或默认安装位置

### 问题3：防病毒软件误报
**可能原因**：PyInstaller打包的exe被误识别
**解决方案**：
1. 将程序添加到防病毒软件白名单
2. 临时关闭实时保护进行测试

### 问题4：首次运行缓慢
**原因**：单文件exe需要解压到临时目录
**说明**：这是正常现象，后续运行会更快

## 🔧 高级部署选项

### 选项1：创建安装程序
使用NSIS或Inno Setup创建专业的安装程序：
```
- 自动检测系统要求
- 自动安装依赖组件
- 创建桌面快捷方式
- 支持卸载
```

### 选项2：便携版部署
```
TemuAutoTool_Portable/
├── TemuAutoTool.exe
├── portable.ini          # 标记为便携版
├── data/
├── config/
└── logs/                 # 日志目录
```

## 📊 兼容性矩阵

| Windows版本 | 兼容性 | 备注 |
|------------|--------|------|
| Windows 11 | ✅ 完全兼容 | 推荐 |
| Windows 10 | ✅ 完全兼容 | 推荐 |
| Windows 8.1 | ⚠️ 基本兼容 | 需要更新运行库 |
| Windows 7 | ⚠️ 有限兼容 | 需要SP1和更新 |

## 🧪 部署前测试清单

- [ ] 在干净的Windows系统上测试
- [ ] 测试不同用户权限（管理员/普通用户）
- [ ] 测试不同Chrome版本
- [ ] 测试网络环境（代理/防火墙）
- [ ] 测试杀毒软件环境
- [ ] 验证所有功能正常工作

## 📝 用户使用说明模板

```
🎯 Temu自动化工具使用说明

📋 运行前准备：
1. 确保已安装Chrome浏览器
2. 准备用户数据CSV文件（参考data/users.csv.copy格式）

🚀 启动程序：
1. 双击 TemuAutoTool.exe
2. 首次运行可能需要几秒钟加载时间
3. 按照程序界面提示操作

❓ 遇到问题：
1. 查看程序目录下的日志文件
2. 确认Chrome浏览器正常工作
3. 联系技术支持
```

## 🔒 安全建议

1. **数字签名**：考虑对exe文件进行数字签名
2. **病毒扫描**：发布前进行全面病毒扫描
3. **完整性校验**：提供文件MD5/SHA256校验值
4. **版本控制**：明确标注版本号和构建日期
