"""
Excel 处理模块 - 基于 openpyxl 的轻量级实现
专为新程序设计，无历史包袱，性能优化
"""

from pathlib import Path
from typing import Any, Dict, List, Optional

from loguru import logger
from openpyxl import Workbook, load_workbook
from openpyxl.worksheet.worksheet import Worksheet


class Excel:
    """基于 openpyxl 的轻量级 Excel 处理类"""

    def __init__(self, excel_name: str):
        """
        初始化 Excel 处理器

        Args:
            excel_name: Excel 文件路径
        """
        self.excel_name = excel_name
        self.excel_path = Path(excel_name)

    def _ensure_file_exists(self) -> bool:
        """确保 Excel 文件存在，如果不存在则创建"""
        try:
            if not self.excel_path.exists():
                # 创建新的工作簿
                wb = Workbook()
                wb.save(self.excel_name)
                logger.info(f"创建新的 Excel 文件: {self.excel_name}")
            return True
        except Exception as e:
            logger.error(f"创建 Excel 文件失败: {str(e)}")
            return False

    def _load_workbook(self) -> Optional[Workbook]:
        """加载工作簿"""
        try:
            if not self._ensure_file_exists():
                return None
            return load_workbook(self.excel_name)
        except Exception as e:
            logger.error(f"加载 Excel 文件失败: {str(e)}")
            return None

    def _get_worksheet(self, wb: Workbook) -> Optional[Worksheet]:
        """获取工作表（默认第一个）"""
        try:
            return wb.active
        except Exception as e:
            logger.error(f"获取工作表失败: {str(e)}")
            return None

    def _worksheet_to_dict_list(self, ws: Worksheet) -> List[Dict[str, Any]]:
        """将工作表转换为字典列表"""
        try:
            if ws.max_row < 2:  # 没有数据行
                return []

            # 获取标题行
            headers = []
            for cell in ws[1]:
                headers.append(cell.value if cell.value is not None else "")

            # 获取数据行
            data_list = []
            for row in ws.iter_rows(min_row=2, values_only=True):
                row_dict = {}
                for i, value in enumerate(row):
                    if i < len(headers):
                        # 处理空值和字符串
                        if value is None:
                            row_dict[headers[i]] = ""
                        elif isinstance(value, str):
                            row_dict[headers[i]] = value.strip()
                        else:
                            row_dict[headers[i]] = value

                data_list.append(row_dict)

            return data_list
        except Exception as e:
            logger.error(f"转换工作表数据失败: {str(e)}")
            return []

    def _get_ordered_columns(self, data: List[Dict[str, Any]]) -> List[str]:
        """
        获取有序的列名列表，尽可能保持原始Excel文件的列顺序

        Args:
            data: 数据列表

        Returns:
            List[str]: 有序的列名列表
        """
        if not data:
            return []

        # 尝试从原始文件读取列顺序
        original_columns = self._get_original_column_order()

        # 收集所有出现的列名
        all_columns = set()
        for row in data:
            all_columns.update(row.keys())

        # 按原始顺序排列已存在的列
        ordered_columns = []
        for col in original_columns:
            if col in all_columns:
                ordered_columns.append(col)
                all_columns.remove(col)

        # 添加新增的列（按第一次出现的顺序）
        for row in data:
            for key in row.keys():
                if key in all_columns:
                    ordered_columns.append(key)
                    all_columns.remove(key)

        return ordered_columns

    def _get_original_column_order(self) -> List[str]:
        """
        获取原始Excel文件的列顺序

        Returns:
            List[str]: 原始列顺序
        """
        try:
            if not Path(self.excel_name).exists():
                return []

            wb = load_workbook(self.excel_name, read_only=True)
            ws = wb.active

            if ws.max_row < 1:
                return []

            # 读取第一行作为列名
            headers = []
            for cell in ws[1]:
                headers.append(cell.value if cell.value is not None else "")

            wb.close()
            return headers

        except Exception as e:
            logger.debug(f"无法读取原始列顺序: {str(e)}")
            return []

    def _dict_list_to_worksheet(self, ws: Worksheet, data: List[Dict[str, Any]]) -> bool:
        """将字典列表写入工作表"""
        try:
            if not data:
                return True

            # 获取所有列名，保持原始顺序
            columns = self._get_ordered_columns(data)

            # 清空工作表
            ws.delete_rows(1, ws.max_row)

            # 写入标题行
            for i, column in enumerate(columns, 1):
                ws.cell(row=1, column=i, value=column)

            # 写入数据行
            for row_idx, row_data in enumerate(data, 2):
                for col_idx, column in enumerate(columns, 1):
                    value = row_data.get(column, "")
                    ws.cell(row=row_idx, column=col_idx, value=value)

            return True
        except Exception as e:
            logger.error(f"写入工作表数据失败: {str(e)}")
            return False

    def write(self, data: List[Dict[str, Any]]) -> bool:
        """
        全新写入数据

        Args:
            data: 要写入的数据列表

        Returns:
            bool: 是否成功
        """
        try:
            wb = Workbook()
            ws = wb.active

            if self._dict_list_to_worksheet(ws, data):
                wb.save(self.excel_name)
                logger.info(f"成功写入 {len(data)} 行数据到 {self.excel_name}")
                return True
            return False
        except Exception as e:
            logger.error(f"写入数据失败: {str(e)}")
            return False

    def append(self, data: List[Dict[str, Any]]) -> bool:
        """
        追加数据

        Args:
            data: 要追加的数据列表

        Returns:
            bool: 是否成功
        """
        try:
            # 读取现有数据
            existing_data = self.load()

            # 合并数据
            combined_data = existing_data + data

            # 写入合并后的数据
            return self.write(combined_data)
        except Exception as e:
            logger.error(f"追加数据失败: {str(e)}")
            return False

    def get(self, field_name: str, field_value: Any) -> Optional[Dict[str, Any]]:
        """
        根据字段值获取行数据

        Args:
            field_name: 字段名
            field_value: 字段值

        Returns:
            Dict[str, Any]: 匹配的行数据，如果未找到返回 None
        """
        try:
            data_list = self.load()

            for row in data_list:
                if row.get(field_name) == field_value:
                    return row

            return None
        except Exception as e:
            logger.error(f"查询数据失败: {str(e)}")
            return None

    def delete(self, field_name: str, field_value: Any) -> bool:
        """
        根据字段值删除行

        Args:
            field_name: 字段名
            field_value: 字段值

        Returns:
            bool: 是否成功
        """
        try:
            data_list = self.load()

            # 过滤掉匹配的行
            filtered_data = [row for row in data_list if row.get(field_name) != field_value]

            # 写入过滤后的数据
            return self.write(filtered_data)
        except Exception as e:
            logger.error(f"删除数据失败: {str(e)}")
            return False

    def delete_by_index(self, row_index: int) -> bool:
        """
        根据索引删除行

        Args:
            row_index: 行索引（从0开始）

        Returns:
            bool: 是否成功
        """
        try:
            data_list = self.load()

            if 0 <= row_index < len(data_list):
                data_list.pop(row_index)
                result = self.write(data_list)
                if result:
                    logger.info(f"已删除索引为 {row_index} 的行")
                return result
            else:
                logger.error(f"无效的行索引: {row_index}")
                return False
        except Exception as e:
            logger.error(f"删除行失败: {str(e)}")
            return False

    def update(self, field_name: str, field_value: Any, updated_values: Dict[str, Any]) -> bool:
        """
        根据字段值更新行数据

        Args:
            field_name: 查找字段名
            field_value: 查找字段值
            updated_values: 要更新的字段和值

        Returns:
            bool: 是否成功
        """
        try:
            data_list = self.load()

            for row in data_list:
                if row.get(field_name) == field_value:
                    row.update(updated_values)
                    break

            return self.write(data_list)
        except Exception as e:
            logger.error(f"更新数据失败: {str(e)}")
            return False

    def update_by_index(self, row_index: int, updated_values: Dict[str, Any]) -> bool:
        """
        根据索引更新行数据

        Args:
            row_index: 行索引（从0开始）
            updated_values: 要更新的字段和值

        Returns:
            bool: 是否成功
        """
        try:
            data_list = self.load()

            if 0 <= row_index < len(data_list):
                data_list[row_index].update(updated_values)
                result = self.write(data_list)
                if result:
                    logger.info(f"已更新索引为 {row_index} 的行")
                return result
            else:
                logger.error(f"无效的行索引: {row_index}")
                return False
        except Exception as e:
            logger.error(f"更新行失败: {str(e)}")
            return False

    def load(self) -> List[Dict[str, Any]]:
        """
        加载所有数据

        Returns:
            List[Dict[str, Any]]: 数据列表
        """
        try:
            wb = self._load_workbook()
            if not wb:
                return []

            ws = self._get_worksheet(wb)
            if not ws:
                return []

            return self._worksheet_to_dict_list(ws)
        except Exception as e:
            logger.error(f"加载数据失败: {str(e)}")
            return []

    def ensure_columns(self, columns_dict: Dict[str, Any]) -> bool:
        """
        确保Excel文件包含指定列，如果不存在则添加

        Args:
            columns_dict: 列名和默认值的字典

        Returns:
            bool: 操作是否成功
        """
        try:
            data_list = self.load()

            if not data_list:
                # 如果文件为空，创建包含指定列的空行
                empty_row = {col: default_val for col, default_val in columns_dict.items()}
                return self.write([empty_row])

            # 检查是否需要添加列
            columns_to_add = {}
            existing_columns = set(data_list[0].keys()) if data_list else set()

            for col_name, default_value in columns_dict.items():
                if col_name not in existing_columns:
                    columns_to_add[col_name] = default_value

            # 如果需要添加列
            if columns_to_add:
                for row in data_list:
                    row.update(columns_to_add)

                return self.write(data_list)

            return True
        except Exception as e:
            logger.error(f"添加Excel列失败: {str(e)}")
            return False

    def get_column_statistics(self, column_name: str) -> Dict[str, Any]:
        """
        获取指定列的统计信息

        Args:
            column_name: 列名

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            data_list = self.load()

            if not data_list:
                return {"error": "文件为空"}

            # 检查列是否存在
            if column_name not in data_list[0]:
                return {"error": f"列 '{column_name}' 不存在"}

            # 统计各种值的数量
            value_counts = {}
            non_empty_count = 0

            for row in data_list:
                value = row.get(column_name, "")
                if value and str(value).strip():
                    non_empty_count += 1
                    value_str = str(value).strip()
                    value_counts[value_str] = value_counts.get(value_str, 0) + 1

            total_rows = len(data_list)
            empty_count = total_rows - non_empty_count

            return {
                "total_rows": total_rows,
                "non_empty_count": non_empty_count,
                "empty_count": empty_count,
                "value_counts": value_counts
            }
        except Exception as e:
            return {"error": f"获取列统计失败: {str(e)}"}


__all__ = ['Excel']
