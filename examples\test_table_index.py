"""
测试表格索引问题

验证JavaScript XPath count()函数与实际表格索引的对应关系
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from biz.temu import Temu
from components import ComponentFactory


def test_table_index():
    """测试表格索引对应关系"""
    
    print("🎯 测试表格索引对应关系")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 测试不同字段的索引
        test_fields = [
            "申报价格",
            "预览图", 
            "SKU分类",
            "建议零售价",
            "制造商建议零售价"
        ]
        
        print("\n🔍 测试各字段的JavaScript XPath索引...")
        for field_title in test_fields:
            print(f"\n🎯 测试字段: {field_title}")
            
            # 使用JavaScript XPath获取列索引
            js_xpath = f"count(//table[contains(@class, 'performance-table')]//thead//th[contains(.,'{field_title}')]/preceding-sibling::th) + 1"
            js_code = f"""
            var xpath = {repr(js_xpath)};
            return document.evaluate(
                xpath, 
                document, 
                null, 
                XPathResult.NUMBER_TYPE, 
                null
            ).numberValue;
            """
            column_count = tab.run_js(js_code)
            print(f"   JavaScript XPath count(): {column_count}")
            
            if column_count and column_count > 0:
                column_index = int(column_count)
                print(f"   使用的表格索引: {column_index}")
                
                # 测试使用这个索引能否找到元素
                test_selector = f"x://table[contains(@class, 'performance-table')]//tbody//td[{column_index}]"
                cell = tab.ele(test_selector, timeout=3)
                
                if cell:
                    print(f"   ✅ 成功找到表格单元格")
                    
                    # 查看单元格内容
                    cell_text = cell.text.strip()[:50]
                    print(f"   单元格内容: {cell_text}...")
                    
                    # 查找输入框或下拉框
                    input_element = cell.ele("x:.//input", timeout=2)
                    select_element = cell.ele("x:.//div[@data-testid='beast-core-input']", timeout=2)
                    
                    if input_element:
                        print(f"   📝 找到input元素: {input_element.attrs}")
                    elif select_element:
                        print(f"   📋 找到select元素: {select_element.attrs}")
                    else:
                        print(f"   ❌ 未找到input或select元素")
                else:
                    print(f"   ❌ 未找到表格单元格")
            else:
                print(f"   ❌ JavaScript XPath未返回有效结果")
        
        # 4. 测试实际的表格结构
        print(f"\n🔍 分析实际表格结构...")
        
        # 查找表格头部
        headers = tab.eles("x://table[contains(@class, 'performance-table')]//thead//th")
        print(f"📋 找到 {len(headers)} 个表头")
        
        for i, header in enumerate(headers, 1):
            header_text = header.text.strip()
            if header_text:
                # 只显示前50个字符
                display_text = header_text[:50] + "..." if len(header_text) > 50 else header_text
                print(f"   第{i}列: {display_text}")
        
        print(f"\n🎉 表格索引测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False


if __name__ == "__main__":
    print("🚀 开始测试表格索引...")
    test_table_index()
    print("\n🎉 测试完成！")
