from time import sleep

from DrissionPage import Chromium
from loguru import logger


class User:
    def __init__(self, page: Chromium):
        self.page = page
        self.home_url = "https://seller.kuajingmaihuo.com/settle/site-main"

    def login(self, phone: str, password: str) -> bool:
        """登录

        Args:
            phone: 手机号
            password: 密码

        Returns
        -------
            bool: 是否登录成功
        """
        try:
            tab = self.page.new_tab(self.home_url)
            tab.wait(3)

            if "login" not in tab.url:
                logger.info(f"已经登录: {phone}")
                return True

            username_input = tab.ele('x://input[@id="usernameId"]')
            username_input.clear(True)
            username_input.input(phone)
            tab.wait(1)

            password_input = tab.ele('x://input[@id="passwordId"]')
            password_input.clear(True)
            password_input.input(password)
            tab.wait(1)

            tab.ele('x://input[@type="checkbox"]').click()
            tab.wait(1)

            tab.ele("x://button[.='登录']").click()
            tab.wait(1)

            if tab.wait.url_change(text="site-main", timeout=30):
                logger.info(f"登录成功: {phone}")
                return True

            logger.error(f"登录失败: {phone}")
            return False
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            return False
