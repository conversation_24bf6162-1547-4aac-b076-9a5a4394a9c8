#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下拉选择输入组件

验证 SelectInputComponent 在实际页面中的功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from loguru import logger
from components.select_input import SelectInputComponent
from utils.chrome import get_page_by_id


def test_select_input_component():
    """测试下拉选择输入组件"""
    print("🔽 测试下拉选择输入组件")
    print("=" * 50)
    
    try:
        # 配置日志
        logger.remove()
        logger.add(
            sys.stdout,
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>",
            level="DEBUG"
        )
        
        # 获取浏览器页面
        logger.info("启动浏览器...")
        page = get_page_by_id("1")
        tab = page.latest_tab
        
        # 打开测试页面
        test_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        logger.info(f"打开测试页面: {test_url}")
        tab.get(test_url)
        tab.wait(3)
        
        # 检查登录状态
        if "login" in tab.url:
            logger.warning("需要登录，请手动登录后继续...")
            input("登录完成后按回车键继续...")
        
        # 创建下拉选择组件
        logger.info("初始化下拉选择组件...")
        select_component = SelectInputComponent(tab)
        
        # 测试数据 - 根据页面中的字段
        test_fields = [
            {
                "title": "安装类型",
                "data": {
                    "value": "自粘",
                    "require": 1,
                    "type": "select_input"
                }
            },
            {
                "title": "材料",
                "data": {
                    "value": "其他材料",
                    "require": 1,
                    "type": "select_input"
                }
            },
            {
                "title": "点缀功能",
                "data": {
                    "value": "装饰",
                    "require": 0,
                    "type": "select_input"
                }
            },
            {
                "title": "形状",
                "data": {
                    "value": "圆形",
                    "require": 0,
                    "type": "select_input"
                }
            },
            {
                "title": "主题",
                "data": {
                    "value": "动物",
                    "require": 0,
                    "type": "select_input"
                }
            }
        ]
        
        print(f"\n📋 测试计划:")
        print(f"  - 测试字段数量: {len(test_fields)}")
        print(f"  - 测试字段: {[field['title'] for field in test_fields]}")
        
        # 逐个测试字段
        success_count = 0
        for i, field_info in enumerate(test_fields, 1):
            field_title = field_info["title"]
            field_data = field_info["data"]
            
            print(f"\n🔽 测试 {i}/{len(test_fields)}: {field_title}")
            print("-" * 30)
            
            try:
                result = select_component.fill_data(field_title, field_data)
                
                if result.is_success():
                    print(f"✅ {field_title}: {result.message}")
                    success_count += 1
                else:
                    print(f"❌ {field_title}: {result.message}")
                
                # 等待一下再测试下一个字段
                tab.wait(1)
                
            except Exception as e:
                print(f"❌ {field_title}: 测试异常 - {str(e)}")
        
        # 测试结果统计
        print(f"\n📊 测试结果:")
        print(f"  - 成功: {success_count}/{len(test_fields)}")
        print(f"  - 成功率: {success_count/len(test_fields)*100:.1f}%")
        
        if success_count == len(test_fields):
            print("🎉 所有字段测试通过！")
            return True
        elif success_count > 0:
            print("⚠️  部分字段测试通过")
            return True
        else:
            print("❌ 所有字段测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        logger.exception("详细错误信息:")
        return False


def test_interactive_mode():
    """交互式测试模式"""
    print("\n🎮 交互式测试模式")
    print("=" * 30)
    
    try:
        # 获取浏览器页面
        page = get_page_by_id("1")
        tab = page.latest_tab
        
        # 创建组件
        select_component = SelectInputComponent(tab)
        
        print("请手动指定要测试的字段和值")
        
        while True:
            print("\n" + "="*40)
            field_title = input("请输入字段标题 (如: 安装类型, 或输入 'q' 退出): ").strip()
            
            if field_title.lower() == 'q':
                break
            
            field_value = input("请输入要选择的值: ").strip()
            
            if not field_title or not field_value:
                print("字段标题和值不能为空")
                continue
            
            # 构造测试数据
            field_data = {
                "value": field_value,
                "require": 0,
                "type": "select_input"
            }
            
            print(f"\n🔽 测试字段: {field_title} = {field_value}")
            
            try:
                result = select_component.fill_data(field_title, field_data)
                
                if result.is_success():
                    print(f"✅ 成功: {result.message}")
                else:
                    print(f"❌ 失败: {result.message}")
                    
            except Exception as e:
                print(f"❌ 异常: {str(e)}")
        
        print("👋 退出交互式测试")
        return True
        
    except Exception as e:
        print(f"❌ 交互式测试失败: {str(e)}")
        return False


def test_field_detection():
    """测试字段检测功能"""
    print("\n🔍 测试字段检测功能")
    print("=" * 30)
    
    try:
        # 获取浏览器页面
        page = get_page_by_id("1")
        tab = page.latest_tab
        
        # 创建组件
        select_component = SelectInputComponent(tab)
        
        # 测试字段列表
        test_field_titles = [
            "安装类型", "材料", "点缀功能", "表面推荐", 
            "可重用性", "图案", "形状", "完成类型", 
            "主题", "品牌", "语言类型"
        ]
        
        print(f"检测页面中的下拉选择字段...")
        
        detected_count = 0
        for field_title in test_field_titles:
            try:
                # 查找字段容器
                field_container = select_component.find_field_container(field_title)
                if field_container:
                    # 查找下拉输入框
                    select_element = select_component._find_select_input_element(field_container, field_title)
                    if select_element:
                        print(f"✅ 检测到字段: {field_title}")
                        detected_count += 1
                    else:
                        print(f"⚠️  找到容器但未找到输入框: {field_title}")
                else:
                    print(f"❌ 未找到字段: {field_title}")
                    
            except Exception as e:
                print(f"❌ 检测字段异常: {field_title} - {str(e)}")
        
        print(f"\n📊 检测结果: {detected_count}/{len(test_field_titles)} 个字段")
        
        return detected_count > 0
        
    except Exception as e:
        print(f"❌ 字段检测失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("🧪 下拉选择输入组件测试")
    print("=" * 50)
    
    try:
        print("请选择测试类型:")
        print("1. 自动化测试")
        print("2. 交互式测试")
        print("3. 字段检测测试")
        print("4. 运行所有测试")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            success = test_select_input_component()
        elif choice == "2":
            success = test_interactive_mode()
        elif choice == "3":
            success = test_field_detection()
        elif choice == "4":
            success1 = test_field_detection()
            success2 = test_select_input_component()
            success3 = test_interactive_mode()
            success = success1 and success2 and success3
        else:
            print("无效选择")
            return
        
        if success:
            print("\n🎉 测试完成！")
        else:
            print("\n❌ 测试失败")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"\n❌ 测试运行失败: {str(e)}")


if __name__ == "__main__":
    main()
