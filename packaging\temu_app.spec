# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 获取项目根目录
# 在PyInstaller中，__file__ 不可用，使用当前工作目录
project_root = Path('.').absolute()

# 检测操作系统
is_windows = sys.platform.startswith('win')
is_macos = sys.platform == 'darwin'
is_linux = sys.platform.startswith('linux')

block_cipher = None

# 数据文件和目录
datas = [
    # 配置文件
    (str(project_root / 'config'), 'config'),
    # 数据文件模板
    (str(project_root / 'data' / 'users.csv.copy'), 'data'),
    # 如果有其他必要的数据文件，在这里添加
]

# 隐藏导入（PyInstaller可能无法自动检测的模块）
hiddenimports = [
    'click',
    'loguru',
    'DrissionPage',
    'openpyxl',
    'requests',
    'faker',
    'pypinyin',
    'cryptography',
    'lxml',
    'cssselect',
    'curl_cffi',
    'psutil',
    'PyAutoGUI',
    'pyperclip',
    'websocket',
    'tldextract',
    'python-dotenv',
    # 项目内部模块
    'biz.temu',
    'biz.user',
    'biz.goods',
    'utils.user_manager',
    'utils.common',
    'utils.chrome',
    'utils.excel',
    'utils.data_utils',
    'utils.logger_config',
    'utils.image_validator',
    'utils.goods_processor',
    'components.base_component',
    'components.component_factory',
    'components.material_center',
    'components.upload_input',
    'components.grid_wrapper',
    'components.decoration',
    'config.image_config',
    'app',
]

# 排除的模块（减少打包大小）
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',      # 已移除，但保留排除以防其他库引入
    'pandas',     # 已移除，但保留排除以防其他库引入
    'scipy',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',     # 测试框架
    'sphinx',     # 文档生成
    'setuptools', # 安装工具
    'distutils',  # 安装工具
    'wheel',      # 打包工具
]

# 根据操作系统添加特定的排除模块
if not is_macos:
    excludes.extend(['pyobjc', 'rubicon'])
if not is_windows:
    excludes.extend(['winsound', 'msvcrt'])
if not is_linux:
    excludes.extend(['pwd', 'grp'])

a = Analysis(
    [str(project_root / 'index.py')],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 根据操作系统设置不同的可执行文件名
exe_name = 'TemuAutoTool'
if is_windows:
    exe_name += '.exe'

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=exe_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 如果有图标文件，在这里指定路径
)

# 如果需要创建目录版本（便于调试）
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='TemuAutoTool'
# )
