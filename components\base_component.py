"""
基础UI组件类

定义所有UI组件的通用接口和方法
"""

from abc import ABC, abstractmethod
from typing import Any, Dict

from DrissionPage._pages.chromium_tab import ChromiumTab
from loguru import logger

from utils.response import ApiResponse, error_response, success_response


class BaseComponent(ABC):
    """UI组件基类"""
    
    def __init__(self, tab: ChromiumTab):
        """
        初始化组件
        
        Args:
            tab: 浏览器标签页对象
        """
        self.tab = tab
        
    @abstractmethod
    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充字段数据的抽象方法
        
        Args:
            field_title: 字段标题
            field_data: 字段数据，包含 value, require, type 等信息
            
        Returns:
            ApiResponse: 操作结果
        """
        pass
    
    @staticmethod
    def validate_required_field(field_title: str, field_data: Dict[str, Any]):
        """
        验证必填字段

        Args:
            field_title: 字段标题
            field_data: 字段数据

        Returns:
            ApiResponse: 如果验证失败返回错误响应，成功返回None
        """
        require = field_data.get("require", 0)
        if require == 1:
            value = field_data.get("value")

            # 安全地处理不同类型的值
            if value is None:
                processed_value = ""
            elif isinstance(value, str):
                processed_value = value.strip()
            else:
                # 对于非字符串类型（如int, float等），转换为字符串后再处理
                processed_value = str(value).strip()

            # 检查值是否为空
            if processed_value == "":
                logger.error(f"必填字段 '{field_title}' 的值不能为空")
                return error_response(
                    f"必填字段 '{field_title}' 的值不能为空",
                    data={"missing_field": field_title, "field_data": field_data}
                )

        return None
    
    def wait_for_element(self, selector: str, timeout: int = 10):
        """
        等待元素出现
        
        Args:
            selector: 元素选择器
            timeout: 超时时间（秒）
            
        Returns:
            元素对象或None
        """
        try:
            element = self.tab.ele(selector, timeout=timeout)
            return element
        except Exception as e:
            logger.warning(f"等待元素失败: {selector}, 错误: {str(e)}")
            return None

    @abstractmethod
    def find_field_container(self, field_title: str):
        pass
    
    @staticmethod
    def log_operation(field_title: str, operation: str, success: bool = True, error_msg: str = ""):
        """
        记录操作日志

        Args:
            field_title: 字段标题
            operation: 操作描述
            success: 是否成功
            error_msg: 错误信息
        """
        if success:
            logger.info(f"✅ {field_title}: {operation}")
        else:
            logger.error(f"❌ {field_title}: {operation} - {error_msg}")
