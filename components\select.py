"""
下拉选择组件

处理下拉选择字段
"""

from typing import Any, Dict
from loguru import logger
from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class SelectComponent(BaseComponent):
    """下拉选择组件"""

    def find_field_container(self, field_title: str):
        """
        查找字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            # 精确查找：从label开始找最近的select容器
            label_selector = f"x://label[contains(text(), '{field_title}')]"
            label_element = self.tab.ele(label_selector, timeout=5)

            if label_element:
                # 从label开始，向上查找包含select的最小容器
                ancestors = label_element.eles("x:./ancestor::div[.//div[@data-testid='beast-core-select']]", timeout=3)

                # 找到包含最少select选项的容器（最精确的容器）
                best_container = None
                min_select_count = float('inf')

                for ancestor in ancestors:
                    select_elements = ancestor.eles("x:.//div[@data-testid='beast-core-select']", timeout=2)
                    select_count = len(select_elements)

                    if select_count > 0 and select_count < min_select_count:
                        min_select_count = select_count
                        best_container = ancestor

                if best_container:
                    logger.debug(f"找到字段容器: {field_title} (包含{min_select_count}个select)")
                    return best_container

            logger.warning(f"未找到字段容器: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找字段容器失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充下拉选择字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")
            
            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            # 查找下拉选择框
            select_element = self._find_select_element(field_container, field_title)
            if not select_element:
                error_msg = f"未找到下拉选择框: {field_title}"
                self.log_operation(field_title, "查找下拉选择框", False, error_msg)
                return error_response(error_msg)

            # 点击打开下拉菜单
            self.tab.run_js("arguments[0].click();", select_element)
            self.tab.wait(3)  # 等待下拉菜单展开

            # 查找并选择选项
            option_selected = self._select_option(value, field_title)
            if not option_selected:
                error_msg = f"未找到选项: {value}"
                self.log_operation(field_title, "选择选项", False, error_msg)
                return error_response(error_msg)

            self.log_operation(field_title, f"选择选项: {value}")
            return success_response(f"成功填充字段 '{field_title}': {value}")
            
        except Exception as e:
            error_msg = f"填充下拉选择字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)

    @staticmethod
    def _find_select_element(field_container, field_title: str):
        """
        在字段容器中查找下拉选择框元素

        Args:
            field_container: 字段容器元素
            field_title: 字段标题

        Returns:
            下拉选择框元素或None
        """
        # 精确的xpath选择器
        selector = "x:.//div[@data-testid='beast-core-select']"

        try:
            element = field_container.ele(selector, timeout=5)
            if element:
                logger.debug(f"找到下拉选择框: {field_title} -> {selector}")
                return element
        except Exception:
            pass

        return None

    def _select_option(self, value: str, field_title: str) -> bool:
        """
        选择下拉菜单中的选项

        Args:
            value: 要选择的值
            field_title: 字段标题

        Returns:
            是否成功选择
        """
        # 精确的选项选择器 - 只在当前打开的下拉框中查找
        option_selector = f"x://li[@role='option' and contains(.,'{value}')]"

        try:
            option = self.tab.ele(option_selector, timeout=5)
            if option:
                # 使用JavaScript点击确保可靠性
                self.tab.run_js("arguments[0].click();", option)
                logger.debug(f"选择选项: {field_title} -> {value}")
                self.tab.wait(1)  # 等待选择生效
                return True
        except Exception as e:
            logger.debug(f"选择选项失败: {str(e)}")

        return False

