❓ Temu自动化工具 V1.0.0 - 常见问题解答

🚀 启动问题
==================

Q1: 双击程序没有反应？
A1: 可能原因及解决方案：
   - 检查是否为64位Windows系统
   - 下载并安装 Visual C++ 运行库：
     https://aka.ms/vs/17/release/vc_redist.x64.exe
   - 临时关闭防病毒软件测试
   - 右键选择"以管理员身份运行"

Q2: 程序启动很慢？
A2: 这是正常现象：
   - 首次启动需要解压文件到临时目录
   - 通常需要5-10秒，请耐心等待
   - 后续启动会更快

Q3: 提示"配置文件加载失败"？
A3: 检查以下项目：
   - 确保config文件夹存在
   - 确保config/fileds.json文件存在
   - 检查文件是否被占用或损坏
   - 重新下载完整程序包

🔐 登录问题
==================

Q4: 无法导入用户数据？
A4: 检查CSV文件格式：
   - 参考data/users.csv.copy模板
   - 确保包含必需的列：手机号、用户名、密码
   - 检查文件编码为UTF-8
   - 确保文件路径正确

Q5: 登录失败或超时？
A5: 可能的解决方案：
   - 检查网络连接是否稳定
   - 确认用户名密码正确
   - 尝试手动登录网站确认账户状态
   - 重启程序重试

🌐 浏览器问题
==================

Q6: 提示Chrome相关错误？
A6: 浏览器问题解决：
   - 下载并安装最新版Chrome浏览器
     下载地址：https://www.google.com/intl/zh-CN/chrome/
   - 确保Chrome在默认安装位置
   - 关闭其他Chrome窗口
   - 重启电脑后重试

Q7: 网页加载失败？
A7: 网络相关问题：
   - 检查网络连接
   - 尝试访问Temu网站确认可用性
   - 检查防火墙设置
   - 如使用代理，请确认代理设置正确

Q8: Chrome浏览器未安装？
A8: 安装Chrome浏览器：
   - 访问：https://www.google.com/intl/zh-CN/chrome/
   - 下载并安装最新版本
   - 安装完成后重启程序
   - Chrome是程序运行的必需组件

📦 商品上传问题
==================

Q9: "详情页装修未打开"错误？
A9: 这是已知问题，解决方案：
   - 等待页面完全加载后重试
   - 检查网络连接稳定性
   - 关闭其他占用资源的程序
   - 程序会自动重试3次

Q10: 图片上传失败？
A10: 图片相关问题：
    - 确认图片格式为JPG/PNG
    - 检查图片文件大小（建议<10MB）
    - 确认图片文件路径正确
    - 检查图片文件是否损坏

Q11: Excel文件读取失败？
A11: 文件格式问题：
    - 确保为.xlsx格式（不是.xls）
    - 检查文件是否被其他程序占用
    - 确认Excel文件结构正确
    - 尝试重新保存Excel文件

🛡️ 安全问题
==================

Q12: 防病毒软件报警？
A12: 这是误报，解决方案：
    - 将程序添加到防病毒软件白名单
    - 程序已通过安全检测，可放心使用
    - 临时关闭实时保护进行测试

Q13: 担心数据安全？
A13: 数据安全保障：
    - 所有数据仅在本地处理
    - 不会上传到第三方服务器
    - 用户密码等敏感信息加密存储
    - 程序开源，代码可审查

🔧 环境问题
==================

Q14: 需要安装Python吗？
A14: Python安装说明：
    - 使用exe版本无需安装Python
    - 如需运行源代码，请安装Python 3.12.10
    - 下载地址：https://www.python.org/ftp/python/3.12.10/python-3.12.10-amd64.exe
    - 安装时建议勾选"Add Python to PATH"

Q15: 缺少运行库怎么办？
A15: 运行库安装：
    - 下载Visual C++ 运行库：
      https://aka.ms/vs/17/release/vc_redist.x64.exe
    - 安装后重启电脑
    - 重新运行程序

⚡ 性能问题
==================

Q16: 程序运行缓慢？
A16: 性能优化建议：
    - 关闭不必要的后台程序
    - 确保有足够的内存空间
    - 检查硬盘空间是否充足
    - 重启电脑清理内存

Q17: 处理大量数据时卡顿？
A17: 大数据处理建议：
    - 分批处理，避免一次处理过多数据
    - 确保网络连接稳定
    - 耐心等待，程序会显示进度
    - 避免在处理过程中操作其他程序

🔧 其他问题
==================

Q18: 如何查看详细日志？
A18: 日志查看方法：
    - 程序目录下的logs文件夹
    - 查看最新的app.log文件
    - 日志包含详细的操作记录和错误信息

Q19: 如何联系技术支持？
A19: 获取帮助的方式：
    - 查看docs文件夹中的详细文档
    - 收集日志文件和错误截图
    - 描述具体的操作步骤和错误现象
    - 提供系统环境信息

💡 使用技巧
==================

✅ 使用前确保Chrome浏览器已安装并为最新版本
✅ 建议在网络稳定的环境下使用
✅ 定期清理logs文件夹中的旧日志
✅ 处理大量数据时建议分批进行
✅ 遇到问题时先查看日志文件
✅ 首次使用建议先阅读使用说明

🔗 重要下载链接
==================

Chrome浏览器：https://www.google.com/intl/zh-CN/chrome/
Python 3.12.10：https://www.python.org/ftp/python/3.12.10/python-3.12.10-amd64.exe
Visual C++运行库：https://aka.ms/vs/17/release/vc_redist.x64.exe

版本：V1.0.0
更新日期：2025年7月
