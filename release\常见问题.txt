❓ Temu自动化工具 - 常见问题解答

🚀 启动问题
==================

Q1: 双击程序没有反应？
A1: 可能原因及解决方案：
   - 检查是否为64位Windows系统
   - 安装 Visual C++ 运行库
   - 临时关闭防病毒软件测试
   - 右键选择"以管理员身份运行"

Q2: 程序启动很慢？
A2: 这是正常现象：
   - 首次启动需要解压文件到临时目录
   - 通常需要5-10秒，请耐心等待
   - 后续启动会更快

Q3: 提示"配置文件加载失败"？
A3: 检查以下项目：
   - 确保config文件夹存在
   - 确保config/fileds.json文件存在
   - 检查文件是否被占用或损坏
   - 重新下载完整程序包

🔐 登录问题
==================

Q4: 无法导入用户数据？
A4: 检查CSV文件格式：
   - 参考data/users.csv.copy模板
   - 确保包含必需的列：手机号、用户名、密码
   - 检查文件编码为UTF-8
   - 确保文件路径正确

Q5: 登录失败或超时？
A5: 可能的解决方案：
   - 检查网络连接是否稳定
   - 确认用户名密码正确
   - 尝试手动登录网站确认账户状态
   - 重启程序重试

🌐 浏览器问题
==================

Q6: 提示Chrome相关错误？
A6: 浏览器问题解决：
   - 安装最新版Chrome浏览器
   - 确保Chrome在默认安装位置
   - 关闭其他Chrome窗口
   - 重启电脑后重试

Q7: 网页加载失败？
A7: 网络相关问题：
   - 检查网络连接
   - 尝试访问Temu网站确认可用性
   - 检查防火墙设置
   - 如使用代理，请确认代理设置正确

📦 商品上传问题
==================

Q8: "详情页装修未打开"错误？
A8: 这是已知问题，解决方案：
   - 等待页面完全加载后重试
   - 检查网络连接稳定性
   - 关闭其他占用资源的程序
   - 程序会自动重试3次

Q9: 图片上传失败？
A9: 图片相关问题：
   - 确认图片格式为JPG/PNG
   - 检查图片文件大小（建议<10MB）
   - 确认图片文件路径正确
   - 检查图片文件是否损坏

Q10: Excel文件读取失败？
A10: 文件格式问题：
    - 确保为.xlsx格式（不是.xls）
    - 检查文件是否被其他程序占用
    - 确认Excel文件结构正确
    - 尝试重新保存Excel文件

🛡️ 安全问题
==================

Q11: 防病毒软件报警？
A11: 这是误报，解决方案：
    - 将程序添加到防病毒软件白名单
    - 程序已通过安全检测，可放心使用
    - 临时关闭实时保护进行测试

Q12: 担心数据安全？
A12: 数据安全保障：
    - 所有数据仅在本地处理
    - 不会上传到第三方服务器
    - 用户密码等敏感信息加密存储
    - 程序开源，代码可审查

⚡ 性能问题
==================

Q13: 程序运行缓慢？
A13: 性能优化建议：
    - 关闭不必要的后台程序
    - 确保有足够的内存空间
    - 检查硬盘空间是否充足
    - 重启电脑清理内存

Q14: 处理大量数据时卡顿？
A14: 大数据处理建议：
    - 分批处理，避免一次处理过多数据
    - 确保网络连接稳定
    - 耐心等待，程序会显示进度
    - 避免在处理过程中操作其他程序

🔧 其他问题
==================

Q15: 如何查看详细日志？
A15: 日志查看方法：
    - 程序目录下的logs文件夹
    - 查看最新的app.log文件
    - 日志包含详细的操作记录和错误信息

Q16: 如何联系技术支持？
A16: 获取帮助的方式：
    - 查看docs文件夹中的详细文档
    - 收集日志文件和错误截图
    - 描述具体的操作步骤和错误现象
    - 提供系统环境信息

💡 使用技巧
==================

✅ 建议在网络稳定的环境下使用
✅ 定期清理logs文件夹中的旧日志
✅ 保持Chrome浏览器为最新版本
✅ 处理大量数据时建议分批进行
✅ 遇到问题时先查看日志文件

版本：v2.1.0
更新日期：2025年7月
