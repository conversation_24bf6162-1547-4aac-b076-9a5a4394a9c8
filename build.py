#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu 自动化工具跨平台打包脚本

支持 Windows、macOS 和 Linux 平台的自动打包
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path


class CrossPlatformBuilder:
    """跨平台打包器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.system = platform.system().lower()
        self.is_windows = self.system == 'windows'
        self.is_macos = self.system == 'darwin'
        self.is_linux = self.system == 'linux'
        
        # 设置平台特定的配置
        self.venv_name = '.venv' if not self.is_windows else '.venv'
        self.python_cmd = 'python3' if not self.is_windows else 'python'
        self.pip_cmd = 'pip3' if not self.is_windows else 'pip'
        
        # 可执行文件名
        self.exe_name = 'TemuAutoTool'
        if self.is_windows:
            self.exe_name += '.exe'
    
    def print_header(self):
        """打印标题"""
        print("🎯 Temu 自动化工具跨平台打包脚本")
        print("=" * 50)
        print(f"🖥️  操作系统: {platform.system()} {platform.release()}")
        print(f"🐍 Python版本: {sys.version.split()[0]}")
        print(f"📁 项目目录: {self.project_root}")
        print()
    
    def check_python(self):
        """检查Python环境"""
        print("📋 检查 Python 环境...")
        try:
            result = subprocess.run([self.python_cmd, '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {self.python_cmd} 未安装或未添加到 PATH")
            return False
    
    def setup_venv(self):
        """设置虚拟环境"""
        print("\n📦 设置虚拟环境...")
        venv_path = self.project_root / self.venv_name
        
        if not venv_path.exists():
            print("🔧 创建虚拟环境...")
            subprocess.run([self.python_cmd, '-m', '.venv', str(venv_path)], check=True)
        else:
            print("✅ 虚拟环境已存在")
        
        return venv_path
    
    def get_venv_python(self, venv_path):
        """获取虚拟环境的Python路径"""
        if self.is_windows:
            return venv_path / 'Scripts' / 'python.exe'
        else:
            return venv_path / 'bin' / 'python'
    
    def get_venv_pip(self, venv_path):
        """获取虚拟环境的pip路径"""
        if self.is_windows:
            return venv_path / 'Scripts' / 'pip.exe'
        else:
            return venv_path / 'bin' / 'pip'
    
    def install_dependencies(self, venv_path):
        """安装依赖"""
        print("\n📥 安装依赖包...")
        pip_path = self.get_venv_pip(venv_path)
        
        # 升级pip
        subprocess.run([str(pip_path), 'install', '--upgrade', 'pip'], check=True)
        
        # 安装项目依赖
        requirements_file = self.project_root / 'requirements.txt'
        if requirements_file.exists():
            subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], check=True)
        
        # 安装PyInstaller
        subprocess.run([str(pip_path), 'install', 'pyinstaller'], check=True)
        
        print("✅ 依赖安装完成")
    
    def clean_build(self):
        """清理构建文件"""
        print("\n🧹 清理之前的构建文件...")
        
        clean_dirs = ['build', 'dist', '__pycache__', 'release']
        for dir_name in clean_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"🗑️  删除: {dir_name}")
        
        # 清理Python缓存
        for pycache in self.project_root.rglob('__pycache__'):
            shutil.rmtree(pycache)
        
        print("✅ 清理完成")
    
    def build_executable(self, venv_path):
        """构建可执行文件"""
        print("\n🔨 开始打包...")
        
        python_path = self.get_venv_python(venv_path)
        spec_file = self.project_root / 'packaging' / 'temu_app.spec'
        
        cmd = [
            str(python_path), '-m', 'PyInstaller',
            str(spec_file),
            '--clean',
            '--noconfirm'
        ]
        
        result = subprocess.run(cmd, cwd=str(self.project_root))
        
        if result.returncode != 0:
            print("❌ 打包失败")
            return False
        
        print("✅ 打包完成")
        return True
    
    def create_release(self):
        """创建发布包"""
        print("\n📁 创建发布目录...")
        
        release_dir = self.project_root / 'release'
        release_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件
        dist_dir = self.project_root / 'dist'
        exe_file = dist_dir / self.exe_name
        
        if exe_file.exists():
            shutil.copy2(exe_file, release_dir)
            print(f"📋 复制可执行文件: {self.exe_name}")
        else:
            print(f"❌ 找不到可执行文件: {exe_file}")
            return False
        
        # 复制必要文件
        self._copy_essential_files(release_dir)
        
        # 创建使用说明
        self._create_readme(release_dir)
        
        print("✅ 发布包创建完成")
        return True
    
    def _copy_essential_files(self, release_dir):
        """复制必要文件"""
        # 复制数据文件
        data_dir = release_dir / 'data'
        data_dir.mkdir(exist_ok=True)
        
        users_template = self.project_root / 'data' / 'users.csv.copy'
        if users_template.exists():
            shutil.copy2(users_template, data_dir)
        
        # 复制配置文件
        config_dir = release_dir / 'config'
        config_dir.mkdir(exist_ok=True)
        
        source_config = self.project_root / 'config'
        if source_config.exists():
            for config_file in source_config.glob('*.json'):
                shutil.copy2(config_file, config_dir)
    
    def _create_readme(self, release_dir):
        """创建使用说明"""
        readme_content = f"""🎯 Temu 自动化工具 {platform.system()} 版

📋 使用方法:
1. 运行 {self.exe_name} 启动程序
2. 首次使用请先导入用户CSV文件
3. 用户CSV文件格式参考 data/users.csv.copy

📁 文件说明:
- {self.exe_name}: 主程序
- data/users.csv.copy: 用户数据模板
- config/: 配置文件目录

🔧 系统要求:
- 操作系统: {platform.system()} {platform.release()}
- 架构: {platform.machine()}

🔧 技术支持:
如遇问题请联系开发者
"""
        
        readme_file = release_dir / '使用说明.txt'
        readme_file.write_text(readme_content, encoding='utf-8')
    
    def test_executable(self):
        """测试可执行文件"""
        print("\n🧪 测试打包结果...")
        
        release_dir = self.project_root / 'release'
        exe_file = release_dir / self.exe_name
        
        if not exe_file.exists():
            print("❌ 可执行文件不存在")
            return False
        
        try:
            # 测试帮助命令
            result = subprocess.run([str(exe_file), '--help'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ 程序运行正常")
                return True
            else:
                print("⚠️  程序可能存在问题")
                print(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⚠️  程序响应超时")
            return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def build(self):
        """执行完整的构建流程"""
        self.print_header()
        
        # 检查Python环境
        if not self.check_python():
            return False
        
        # 设置虚拟环境
        venv_path = self.setup_venv()
        
        # 安装依赖
        self.install_dependencies(venv_path)
        
        # 清理构建文件
        self.clean_build()
        
        # 构建可执行文件
        if not self.build_executable(venv_path):
            return False
        
        # 创建发布包
        if not self.create_release():
            return False
        
        # 测试可执行文件
        self.test_executable()
        
        # 显示结果
        print("\n🎉 打包流程完成！")
        print(f"📦 可执行文件: release/{self.exe_name}")
        print("📋 使用说明: release/使用说明.txt")
        print()
        
        return True


def main():
    """主函数"""
    builder = CrossPlatformBuilder()
    
    try:
        success = builder.build()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
