#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu 自动化工具跨平台打包脚本

支持 Windows、macOS 和 Linux 平台的自动打包
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path


class CrossPlatformBuilder:
    """跨平台打包器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.system = platform.system().lower()
        self.is_windows = self.system == 'windows'
        self.is_macos = self.system == 'darwin'
        self.is_linux = self.system == 'linux'

        # 设置平台特定的配置
        self.venv_name = self._detect_venv_directory()
        self.python_cmd = 'python3' if not self.is_windows else 'python'
        self.pip_cmd = 'pip3' if not self.is_windows else 'pip'

        # 可执行文件名
        self.exe_name = 'TemuAutoTool'
        if self.is_windows:
            self.exe_name += '.exe'

    def _detect_venv_directory(self):
        """检测虚拟环境目录名称"""
        # 支持的虚拟环境目录名称，按优先级排序
        venv_candidates = ['.venv', 'venv', 'env', '.env']

        for venv_name in venv_candidates:
            venv_path = self.project_root / venv_name
            if venv_path.exists() and venv_path.is_dir():
                print(f"🔍 检测到虚拟环境目录: {venv_name}")
                return venv_name

        # 如果没有找到现有的虚拟环境，默认使用 .venv
        return '.venv'

    def print_header(self):
        """打印标题"""
        print("🎯 Temu 自动化工具跨平台打包脚本")
        print("=" * 50)
        print(f"🖥️  操作系统: {platform.system()} {platform.release()}")
        print(f"🐍 Python版本: {sys.version.split()[0]}")
        print(f"📁 项目目录: {self.project_root}")
        print()
    
    def check_python(self):
        """检查Python环境"""
        print("📋 检查 Python 环境...")
        try:
            result = subprocess.run([self.python_cmd, '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {self.python_cmd} 未安装或未添加到 PATH")
            return False
    
    def setup_venv(self):
        """设置虚拟环境"""
        print("\n📦 设置虚拟环境...")
        venv_path = self.project_root / self.venv_name

        if not venv_path.exists():
            print(f"🔧 创建虚拟环境: {self.venv_name}")
            subprocess.run([self.python_cmd, '-m', 'venv', str(venv_path)], check=True)
        else:
            print(f"✅ 虚拟环境已存在: {self.venv_name}")

        return venv_path
    
    def get_venv_python(self, venv_path):
        """获取虚拟环境的Python路径"""
        if self.is_windows:
            return venv_path / 'Scripts' / 'python.exe'
        else:
            return venv_path / 'bin' / 'python'
    
    def get_venv_pip(self, venv_path):
        """获取虚拟环境的pip路径"""
        if self.is_windows:
            return venv_path / 'Scripts' / 'pip.exe'
        else:
            return venv_path / 'bin' / 'pip'
    
    def install_dependencies(self, venv_path):
        """安装依赖"""
        print("\n📥 安装依赖包...")
        pip_path = self.get_venv_pip(venv_path)

        # 升级pip
        subprocess.run([str(pip_path), 'install', '--upgrade', 'pip'], check=True)

        # 选择合适的requirements文件
        requirements_file = self._get_requirements_file()
        if requirements_file and requirements_file.exists():
            print(f"📋 使用依赖文件: {requirements_file.name}")
            subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], check=True)
        else:
            print("⚠️  未找到requirements文件，仅安装PyInstaller")
            # 安装PyInstaller
            subprocess.run([str(pip_path), 'install', 'pyinstaller'], check=True)

        print("✅ 依赖安装完成")

    def _get_requirements_file(self):
        """根据平台选择合适的requirements文件"""
        if self.is_windows:
            # Windows环境优先使用专用的requirements文件
            windows_req = self.project_root / 'packaging' / 'requirements-windows.txt'
            if windows_req.exists():
                return windows_req

        # 回退到通用的requirements文件
        general_req = self.project_root / 'requirements.txt'
        if general_req.exists():
            return general_req

        return None

    def clean_build(self):
        """清理构建文件"""
        print("\n🧹 清理之前的构建文件...")
        
        clean_dirs = ['build', 'dist', '__pycache__', 'release']
        for dir_name in clean_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"🗑️  删除: {dir_name}")
        
        # 清理Python缓存
        for pycache in self.project_root.rglob('__pycache__'):
            shutil.rmtree(pycache)
        
        print("✅ 清理完成")
    
    def build_executable(self, venv_path):
        """构建可执行文件"""
        print("\n🔨 开始打包...")
        
        python_path = self.get_venv_python(venv_path)
        spec_file = self.project_root / 'packaging' / 'temu_app.spec'
        
        cmd = [
            str(python_path), '-m', 'PyInstaller',
            str(spec_file),
            '--clean',
            '--noconfirm'
        ]
        
        result = subprocess.run(cmd, cwd=str(self.project_root))
        
        if result.returncode != 0:
            print("❌ 打包失败")
            return False
        
        print("✅ 打包完成")
        return True
    
    def create_release(self):
        """创建发布包"""
        print("\n📁 创建发布目录...")
        
        release_dir = self.project_root / 'release'
        release_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件
        dist_dir = self.project_root / 'dist'
        exe_file = dist_dir / self.exe_name
        
        if exe_file.exists():
            shutil.copy2(exe_file, release_dir)
            print(f"📋 复制可执行文件: {self.exe_name}")
        else:
            print(f"❌ 找不到可执行文件: {exe_file}")
            return False
        
        # 复制必要文件
        self._copy_essential_files(release_dir)
        
        # 创建使用说明
        self._create_readme(release_dir)
        
        print("✅ 发布包创建完成")
        return True
    
    def _copy_essential_files(self, release_dir):
        """复制必要文件"""
        # 复制数据文件
        data_dir = release_dir / 'data'
        data_dir.mkdir(exist_ok=True)
        
        # 复制用户数据模板（使用users.csv）
        users_template = self.project_root / 'data' / 'users.csv.copy'
        if users_template.exists():
            # 复制为users.csv
            target_users_file = data_dir / 'users.csv'
            shutil.copy2(users_template, target_users_file)
            print(f"📋 复制用户数据模板: users.csv")

        # 复制商品目录模板
        templates_dir = self.project_root / 'templates'
        if templates_dir.exists():
            target_templates_dir = release_dir / 'templates'
            shutil.copytree(templates_dir, target_templates_dir)
            print(f"📁 复制商品目录模板: templates/")
        
        # 复制配置文件
        config_dir = release_dir / 'config'
        config_dir.mkdir(exist_ok=True)
        
        source_config = self.project_root / 'config'
        if source_config.exists():
            for config_file in source_config.glob('*.json'):
                shutil.copy2(config_file, config_dir)
    
    def _create_readme(self, release_dir):
        """创建使用说明"""
        readme_content = f"""🎯 Temu自动化工具 V1.0.0 Windows版

⚠️ 运行前必需软件安装
==================

本程序需要以下软件支持，请确保已正确安装：

1. 🌐 Chrome浏览器（必需）
   - 下载地址：https://www.google.com/intl/zh-CN/chrome/
   - 说明：用于网页自动化操作，必须安装最新版本

📋 快速开始
==================

1. 📥 安装必需软件
   - 下载并安装Chrome浏览器
   - 确保Chrome为最新版本

2. 🚀 启动程序
   - 双击 {self.exe_name} 启动程序
   - 首次启动可能需要5-10秒加载时间

3. 📊 导入用户数据
   - 选择菜单 [1] 导入用户
   - 准备CSV格式的用户数据文件
   - 参考 data/users.csv 模板格式

4. 🔐 登录账户
   - 选择菜单 [2] 选择用户登录
   - 从用户列表中选择要使用的账户

5. 📦 上传商品
   - 选择菜单 [3] 上传商品
   - 选择包含商品数据的文件夹路径
   - 可以使用 templates/goods_template/ 作为目录结构参考

📁 商品目录格式要求
==================

商品数据文件夹结构示例：
D:\\temu\\goods\\
├── 马桶贴\\                    ← 商品分类目录（对应Temu中的分类）
│   ├── 马桶贴模版.xlsx          ← 商品数据Excel文件
│   └── HZ-M083\\               ← 商品货号目录
│       ├── HZ-M083-1.png       ← 商品图片（货号-序号.扩展名）
│       ├── HZ-M083-2.png       ← 支持jpg、png、jpeg、gif、bmp格式
│       ├── HZ-M083-3.png
│       ├── HZ-M083-4.png
│       └── HZ-M083-5.png
├── 圆柱体包装\\                ← 固定包装目录
│   ├── 1.jpg                     ← 包装图片（序号.jpg）
│   ├── 2.png
│   └── ...
└── 长方体包装\\                ← 固定包装目录
    ├── 1.jpg                     ← 包装图片（序号.jpg）
    ├── 2.png
    └── ...

重要说明：
- 商品分类目录名必须与Temu中的分类名称一致
- 圆柱体包装、长方体包装是固定目录名，不可更改
- 商品货号目录：以实际商品货号命名，如 HZ-M083、ABC-001 等
- 商品图片命名：使用 货号-1.png, 货号-2.png 等格式
- 包装图片命名格式：序号.扩展名（如：1.jpg, 2.png）

📁 文件说明
==================

- {self.exe_name}: 主程序（包含所有依赖）
- data/users.csv: 用户数据模板文件
- config/fileds.json: 字段配置文件
- templates/goods_template/: 商品目录结构模板
- logs/: 日志文件目录（自动创建）

🔧 系统要求
==================

最低要求：
- 操作系统: Windows 10 (64位) 或更高版本
- 内存: 4GB RAM (推荐 8GB)
- 存储: 500MB 可用磁盘空间
- 网络: 稳定的互联网连接
- Chrome浏览器: 最新版本（必需）

❓ 常见问题
==================

Q: 程序无法启动？
A: 1. 确认系统为64位Windows
   2. 安装Chrome浏览器
   3. 临时关闭防病毒软件测试

Q: Chrome相关错误？
A: 1. 确认已安装Chrome浏览器
   2. 更新Chrome到最新版本
   3. 关闭其他Chrome窗口

� 技术支持
==================

如遇问题请：
1. 查看 logs/ 目录下的日志文件
2. 确认Chrome浏览器正常工作
3. 检查商品目录结构是否正确
4. 确认网络连接状态

版本：V1.0.0
更新日期：2025年7月
"""
        
        readme_file = release_dir / '使用说明.txt'
        readme_file.write_text(readme_content, encoding='utf-8')
    
    def test_executable(self):
        """测试可执行文件"""
        print("\n🧪 测试打包结果...")
        
        release_dir = self.project_root / 'release'
        exe_file = release_dir / self.exe_name
        
        if not exe_file.exists():
            print("❌ 可执行文件不存在")
            return False
        
        try:
            # 测试帮助命令
            result = subprocess.run([str(exe_file), '--help'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ 程序运行正常")
                return True
            else:
                print("⚠️  程序可能存在问题")
                print(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⚠️  程序响应超时")
            return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def build(self):
        """执行完整的构建流程"""
        self.print_header()
        
        # 检查Python环境
        if not self.check_python():
            return False
        
        # 设置虚拟环境
        venv_path = self.setup_venv()
        
        # 安装依赖
        self.install_dependencies(venv_path)
        
        # 清理构建文件
        self.clean_build()
        
        # 构建可执行文件
        if not self.build_executable(venv_path):
            return False
        
        # 创建发布包
        if not self.create_release():
            return False
        
        # 测试可执行文件
        self.test_executable()
        
        # 显示结果
        print("\n🎉 打包流程完成！")
        print(f"📦 可执行文件: release/{self.exe_name}")
        print("📋 使用说明: release/使用说明.txt")
        print()
        
        return True


def main():
    """主函数"""
    builder = CrossPlatformBuilder()
    
    try:
        success = builder.build()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
