# Build.py 改进说明

## 改进概述

针对用户提出的两个问题，对 `build.py` 文件进行了以下改进：

1. **Windows环境下使用专用的requirements文件**
2. **支持多种虚拟环境目录名称并增加存在性判断**

## 具体改进内容

### 1. Windows专用依赖文件支持

#### 问题描述
- 原代码在Windows环境下仍使用通用的 `requirements.txt`
- 项目中已存在 `packaging/requirements-windows.txt` 但未被使用

#### 解决方案
- 新增 `_get_requirements_file()` 方法
- Windows环境下优先使用 `packaging/requirements-windows.txt`
- 如果Windows专用文件不存在，回退到通用的 `requirements.txt`

#### 代码变更
```python
def _get_requirements_file(self):
    """根据平台选择合适的requirements文件"""
    if self.is_windows:
        # Windows环境优先使用专用的requirements文件
        windows_req = self.project_root / 'packaging' / 'requirements-windows.txt'
        if windows_req.exists():
            return windows_req
    
    # 回退到通用的requirements文件
    general_req = self.project_root / 'requirements.txt'
    if general_req.exists():
        return general_req
    
    return None
```

### 2. 多种虚拟环境目录支持

#### 问题描述
- 原代码硬编码使用 `.venv` 目录名
- 项目中实际存在 `venv` 目录
- 缺少目录存在性判断

#### 解决方案
- 新增 `_detect_venv_directory()` 方法
- 支持多种常见的虚拟环境目录名称：`.venv`, `venv`, `env`, `.env`
- 按优先级顺序检测已存在的目录
- 如果都不存在，默认使用 `.venv`

#### 代码变更
```python
def _detect_venv_directory(self):
    """检测虚拟环境目录名称"""
    # 支持的虚拟环境目录名称，按优先级排序
    venv_candidates = ['.venv', 'venv', 'env', '.env']
    
    for venv_name in venv_candidates:
        venv_path = self.project_root / venv_name
        if venv_path.exists() and venv_path.is_dir():
            print(f"🔍 检测到虚拟环境目录: {venv_name}")
            return venv_name
    
    # 如果没有找到现有的虚拟环境，默认使用 .venv
    return '.venv'
```

### 3. 其他改进

#### 更好的用户反馈
- 在依赖安装时显示使用的requirements文件名
- 在虚拟环境设置时显示检测到的目录名
- 修复了虚拟环境创建命令（`-m venv` 而不是 `-m .venv`）

#### 错误处理
- 当找不到requirements文件时，仍会安装PyInstaller
- 提供清晰的警告信息

## 测试结果

### 功能验证
✅ 虚拟环境目录检测正常工作
✅ Windows专用requirements文件被正确选择和使用
✅ 依赖安装成功完成
✅ 向后兼容性保持良好

### 测试输出示例
```
🔍 检测到虚拟环境目录: .venv
📋 使用依赖文件: requirements-windows.txt
✅ 依赖安装完成
```

## 文件对比

### requirements.txt vs requirements-windows.txt
- `requirements-windows.txt` 移除了macOS特定依赖（如pyobjc相关包）
- 添加了Windows特定依赖（pyinstaller, pywin32）
- 优化了包的版本选择

## 使用方法

改进后的 `build.py` 使用方法保持不变：

```bash
python build.py
```

脚本会自动：
1. 检测现有的虚拟环境目录
2. 根据操作系统选择合适的requirements文件
3. 继续执行原有的构建流程

## 兼容性

- ✅ 向后兼容：如果项目中没有Windows专用文件，会使用通用文件
- ✅ 跨平台：非Windows系统仍使用原有逻辑
- ✅ 灵活性：支持多种虚拟环境目录命名习惯
