#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件上传组件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from biz.temu import Temu
from components.upload_input import UploadInputComponent

def test_upload_input():
    """测试文件上传组件"""

    print("🎯 测试文件上传组件")
    print("=" * 50)

    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False

        page = temu.page
        print("✅ 登录成功")

        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")

        # 3. 创建UploadInput组件实例
        print("\n🔧 创建UploadInputComponent实例...")
        upload_component = UploadInputComponent(tab)

        # 4. 测试外包装图片上传
        test_cases = [
            ("外包装图片", {
                "value": "/Users/<USER>/Desktop/temu/长方体包装/",
                "require": 1,
                "type": "upload_input"
            }),
        ]

        for field_title, data in test_cases:
            print(f"\n🎯 测试: {field_title}")
            print(f"📁 路径: {data['value']}")

            # 先测试文件路径解析
            file_paths = upload_component._get_file_paths(data['value'])
            print(f"📋 找到 {len(file_paths)} 个文件:")
            for path in file_paths:
                print(f"   - {os.path.basename(path)}")

            # 验证文件
            valid_paths = upload_component._validate_file_paths(file_paths)
            print(f"✅ 验证通过 {len(valid_paths)} 个文件")

            if not valid_paths:
                print("❌ 没有有效文件，跳过上传测试")
                continue

            # 测试填充数据
            result = upload_component.fill_data(field_title, data)

            if result.is_success():
                print(f"✅ {field_title} 成功: {result.message}")
            else:
                print(f"❌ {field_title} 失败: {result.message}")

            tab.wait(3)

        print(f"\n🎉 文件上传组件测试完成！")

        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False

if __name__ == "__main__":
    test_upload_input()
