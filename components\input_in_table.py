"""
表格内文本输入组件

处理表格内的文本输入字段，支持JavaScript XPath精确定位
"""

from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class InputInTableComponent(BaseComponent):
    """表格内文本输入组件"""

    def _execute_js_xpath(self, xpath_expression: str):
        """
        执行JavaScript XPath表达式

        Args:
            xpath_expression: XPath表达式

        Returns:
            执行结果
        """
        try:
            # 使用repr()来安全地传递XPath表达式，避免引号冲突
            js_code = f"""
            var xpath = {repr(xpath_expression)};
            return document.evaluate(
                xpath,
                document,
                null,
                XPathResult.NUMBER_TYPE,
                null
            ).numberValue;
            """
            result = self.tab.run_js(js_code)
            logger.debug(f"JavaScript XPath执行结果: {xpath_expression} -> {result}")
            return result
        except Exception as e:
            logger.error(f"JavaScript XPath执行失败: {xpath_expression} - {str(e)}")
            return None

    def find_field_container(self, field_title: str):
        """
        查找表格内的输入字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:

            js_xpath = f"count(//th[.//*[normalize-space(text())='{field_title}']]/preceding-sibling::th | //th[normalize-space(text())='{field_title}']/preceding-sibling::th) + 1"
            column_count = self._execute_js_xpath(js_xpath)

            if column_count and column_count > 0:
                column_index = int(column_count)
                logger.debug(f"找到字段列索引: {field_title} -> 第{column_index}列")

                # 根据列索引查找对应的输入框
                table_selector = f"x://table[.//*[contains(normalize-space(text()), '{field_title}')]]//tbody//td[{column_index}]//input"
                element = self.tab.ele(table_selector, timeout=3)
                if element:
                    logger.debug(f"找到表格输入框(包含匹配): {field_title}")
                    return element

            logger.warning(f"未找到字段容器: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找字段容器失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充表格内文本输入字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")

            self.tab.wait(0.5)

            # 查找字段容器或直接找到输入框
            input_element = self.find_field_container(field_title)
            if not input_element:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            # 清空并输入新值
            input_element.clear(True)
            input_element.input(str(value))
            
            self.log_operation(field_title, f"输入文本: {value}")
            return success_response(f"成功填充字段 '{field_title}': {value}")
            
        except Exception as e:
            error_msg = f"填充表格内文本输入字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)
    
    def _find_input_element(self, field_container, field_title: str):
        """
        在字段容器中查找输入框元素

        Args:
            field_container: 字段容器元素
            field_title: 字段标题

        Returns:
            输入框元素或None
        """
        # 精确的input选择器，基于实际测试验证
        selectors = [
            "x:.//input[@type='text']",  # 主要选择器：文本输入框
            "x:.//input[not(@type)]",    # 备选：无type属性的input
            "x:.//textarea"              # 备选：文本域
        ]

        for selector in selectors:
            try:
                element = field_container.ele(selector, timeout=2)
                if element:
                    logger.debug(f"找到输入框: {field_title} -> {selector}")
                    return element
            except Exception:
                continue

        return None
