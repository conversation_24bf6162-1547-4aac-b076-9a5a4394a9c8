import os
from typing import Any, Dict, Optional

from loguru import logger

from biz.goods import Goods
from biz.user import User
from utils.chrome import get_page_by_id
from utils.common import get_project_root_path
from utils.data_utils import DataUtil
from utils.response import ApiResponse, error_response
from utils.user_manager import UserManager


class Temu:
    def __init__(self, index: str, user_data: Optional[Dict[str, str]] = None):
        """
        初始化Temu实例

        Args:
            index: 用户索引
            user_data: 用户数据字典，如果提供则直接使用，否则从CSV文件读取
        """
        self.index = index
        self.page = get_page_by_id(index)
        self.user = User(self.page)
        self.goods = Goods(self.page)

        # 如果提供了用户数据，直接使用；否则从CSV文件读取
        if user_data:
            self.data = user_data
        else:
            self.data_util = DataUtil(os.path.join(get_project_root_path(), "data", "users.csv"))
            self.data = self.data_util.get(index)

    @classmethod
    def from_user_manager(cls, index: str, user_manager: UserManager) -> Optional['Temu']:
        """
        从用户管理器创建Temu实例

        Args:
            index: 用户索引
            user_manager: 用户管理器实例

        Returns:
            Temu实例，如果用户不存在返回None
        """
        user_data = user_manager.get_user_by_index(index)
        if not user_data:
            logger.error(f"未找到索引为 {index} 的用户")
            return None

        return cls(index, user_data)

    def login(self) -> bool:
        """TEMU平台登录实现"""
        try:
            phone = self.data.get("phone")
            password = self.data.get("password")
            if not phone or not password:
                logger.error(f"登录失败, phone={phone}, password={password}")
                return False

            logger.info(f"正在使用手机号 {phone} 登录...")
            return self.user.login(phone, password)
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            return False

    def select_category(self, category_name: str) -> ApiResponse:
        """
        选择商品分类

        Args:
            category_name: 分类名称

        Returns:
            ApiResponse: 选择结果
        """
        return self.goods.select_category(category_name)

    def create_goods(self, goods_data: dict[str, Any]) -> ApiResponse:
        """创建商品

        Args:
            goods_data: 商品数据字典

        Returns:
            ApiResponse: 标准化响应对象，包含 code, message, success, data 字段
        """
        try:
            logger.info("开始创建商品")

            # 验证必填字段
            validation_error = self._validate_required_fields(goods_data)
            if validation_error:
                return validation_error

            # 调用 Goods 类的 create 方法，它现在返回 ApiResponse
            return self.goods.create(goods_data)
        except Exception as e:
            logger.error(f"创建商品失败: {str(e)}")
            return error_response(f"创建商品失败: {str(e)}", data={"goods_data": goods_data})
        finally:
            logger.info("创建商品流程结束")

    @staticmethod
    def _validate_required_fields(goods_data: dict[str, Any]) -> ApiResponse | None:
        """验证必填字段

        Args:
            goods_data: 商品数据字典

        Returns:
            ApiResponse: 如果验证失败返回错误响应，验证成功返回 None
        """
        for field_name, field_data in goods_data.items():
            if not isinstance(field_data, dict):
                continue

            # 检查是否为必填字段
            require = field_data.get("require", 0)
            if require == 1:
                value = field_data.get("value")

                # 检查值是否为空
                if value is None or value == "" or value == [] or value == {}:
                    logger.error(f"必填字段 '{field_name}' 的值不能为空")
                    return error_response(
                        f"必填字段 '{field_name}' 的值不能为空",
                        data={"missing_field": field_name, "field_data": field_data}
                    )

                # 检查字符串是否只包含空白字符
                if isinstance(value, str) and value.strip() == "":
                    logger.error(f"必填字段 '{field_name}' 的值不能为空白字符")
                    return error_response(
                        f"必填字段 '{field_name}' 的值不能为空白字符",
                        data={"missing_field": field_name, "field_data": field_data}
                    )

        return None