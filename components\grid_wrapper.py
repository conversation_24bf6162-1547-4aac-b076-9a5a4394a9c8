"""
图片网格组件

处理商品轮播图等图片网格上传字段
"""

import os
from pathlib import Path
from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from .material_center import MaterialCenterComponent
from utils.response import ApiResponse, error_response, success_response


class GridWrapperComponent(BaseComponent):
    """图片网格组件"""

    def __init__(self, tab):
        """
        初始化图片网格组件

        Args:
            tab: 浏览器标签页对象
        """
        super().__init__(tab)
        self.component_name = "GridWrapper"

        # 初始化素材中心组件
        self.material_center = MaterialCenterComponent(tab)

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充图片网格字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error
            
            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")
            
            # 获取图片文件路径
            image_paths = self._get_image_paths(value)
            if len(image_paths) == 0:
                error_msg = f"未找到有效的图片文件: {value}"
                self.log_operation(field_title, "验证图片路径", False, error_msg)
                return error_response(error_msg)
            
            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            # 点击字段容器以激活
            field_container.click()
            self.tab.wait(1)

            # 使用素材中心组件上传图片
            self.log_operation(field_title, f"开始上传 {len(image_paths)} 张图片")
            upload_result = self.material_center.complete_upload_flow(
                image_paths=image_paths,
                select_count=min(len(image_paths), 5)  # 最多选择5张
            )

            if not upload_result.is_success():
                error_msg = f"素材中心上传失败: {upload_result.message}"
                self.log_operation(field_title, "素材中心上传", False, error_msg)
                return error_response(error_msg)

            uploaded_count = upload_result.data.get("selected_count", 0) if upload_result.data else 0
            self.log_operation(field_title, f"上传图片: {uploaded_count} 张")
            return success_response(f"成功上传 {uploaded_count} 张图片到字段 '{field_title}'")
            
        except Exception as e:
            error_msg = f"填充图片网格字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)
    
    @staticmethod
    def _get_image_paths(value) -> list[str]:
        """
        获取图片文件路径列表（图片应该已经在数据处理阶段重命名为SKU格式）

        Args:
            value: 图片路径值

        Returns:
            图片文件路径列表（最多5张）
        """
        if not isinstance(value, str):
            return []

        path = Path(value)
        if not path.is_dir():
            return [str(path)] if path.is_file() else []

        # 获取文件夹名（SKU号）和所有图片文件
        folder_name = path.name
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        image_files = [f for f in path.iterdir()
                      if f.is_file() and f.suffix.lower() in image_extensions]

        if not image_files:
            logger.warning(f"文件夹 {folder_name} 中没有找到图片文件")
            return []

        # 查找SKU格式的图片文件 (如: HZ-A138-1.png)
        sku_files = []
        for file_path in image_files:
            stem = file_path.stem
            if stem.startswith(f"{folder_name}-") and stem.split('-')[-1].isdigit():
                seq = int(stem.split('-')[-1])
                if 1 <= seq <= 5:
                    sku_files.append((seq, file_path))

        if not sku_files:
            logger.error(f"文件夹 {folder_name} 中没有找到符合SKU格式的图片文件")
            return []

        # 按序号排序并返回路径
        sku_files.sort()
        result_paths = [str(file_path) for _, file_path in sku_files[:5]]

        logger.info(f"找到 {len(result_paths)} 张SKU格式图片: {folder_name}")
        return result_paths

    @staticmethod
    def _find_grid_container(field_container, field_title: str):
        """
        在字段容器中查找图片网格容器

        Args:
            field_container: 字段容器元素
            field_title: 字段标题

        Returns:
            图片网格容器元素或None
        """
        # 尝试多种选择器查找图片网格容器
        selectors = [
            ".//div[contains(@class, 'grid')]",
            ".//div[contains(@class, 'image-grid')]",
            ".//div[contains(@class, 'upload-grid')]",
            ".//div[contains(@class, 'photo-grid')]",
            ".//div[contains(@class, 'gallery')]",
            ".//ul[contains(@class, 'grid')]",
            ".//div[contains(@class, 'wrapper')]"
        ]

        for selector in selectors:
            try:
                element = field_container.ele(selector, timeout=2)
                if element:
                    logger.debug(f"找到图片网格容器: {field_title} -> {selector}")
                    return element
            except Exception as e:
                continue

        # 如果没找到专门的网格容器，返回字段容器本身
        return field_container

    def _upload_images_to_grid(self, grid_container, image_paths: list[str], field_title: str) -> int:
        """
        上传图片到网格

        Args:
            grid_container: 网格容器元素
            image_paths: 图片路径列表
            field_title: 字段标题

        Returns:
            成功上传的图片数量
        """
        uploaded_count = 0

        try:
            # 特殊处理商品轮播图：先点击素材中心
            if field_title == "商品轮播图":
                success = self._handle_material_center_upload(image_paths, field_title)
                return len(image_paths) if success else 0

            # 通用的网格上传逻辑
            upload_areas = self._find_upload_areas(grid_container)

            for i, image_path in enumerate(image_paths):
                if i >= len(upload_areas):
                    logger.warning(f"图片数量超过上传区域数量: {len(image_paths)} > {len(upload_areas)}")
                    break

                upload_area = upload_areas[i]
                success = self._upload_single_image(upload_area, image_path, field_title, i + 1)
                if success:
                    uploaded_count += 1
                    self.tab.wait(1)  # 等待上传完成
                else:
                    logger.warning(f"第 {i + 1} 张图片上传失败: {image_path}")

            return uploaded_count

        except Exception as e:
            logger.error(f"批量上传图片失败: {field_title} -> {str(e)}")
            return uploaded_count
    
    @staticmethod
    def _find_upload_areas(grid_container):
        """
        查找网格中的所有上传区域
        
        Args:
            grid_container: 网格容器元素
            
        Returns:
            上传区域元素列表
        """
        upload_areas = []
        
        # 尝试多种选择器查找上传区域
        selectors = [
            ".//input[@type='file']",
            ".//div[contains(@class, 'upload')]",
            ".//div[contains(@class, 'add')]",
            ".//button[contains(@class, 'upload')]",
            ".//div[contains(text(), '上传')]",
            ".//div[contains(text(), '添加')]",
            ".//div[contains(@class, 'placeholder')]"
        ]
        
        for selector in selectors:
            try:
                elements = grid_container.eles(selector, timeout=2)
                if elements:
                    upload_areas.extend(elements)
                    break  # 找到一种类型的上传区域就停止
            except Exception:
                continue
        
        return upload_areas
    
    def _upload_single_image(self, upload_area, image_path: str, field_title: str, index: int) -> bool:
        """
        上传单张图片
        
        Args:
            upload_area: 上传区域元素
            image_path: 图片路径
            field_title: 字段标题
            index: 图片索引
            
        Returns:
            是否成功上传
        """
        try:
            # 如果是input[type=file]，直接设置文件
            if upload_area.tag.lower() == 'input' and upload_area.attr('type') == 'file':
                upload_area.input(image_path)
                logger.debug(f"上传第 {index} 张图片: {os.path.basename(image_path)}")
                return True
            
            # 如果是其他元素，点击后处理文件选择
            upload_area.click()
            self.tab.wait(1)
            
            # 这里可能需要处理系统文件选择对话框
            logger.warning(f"需要手动处理文件选择对话框: {field_title} 第 {index} 张图片")
            return False
            
        except Exception as e:
            logger.error(f"上传第 {index} 张图片失败: {str(e)}")
            return False

    def _handle_material_center_upload(self, image_paths: list[str], field_title: str) -> bool:
        """
        处理素材中心的图片上传流程

        Args:
            image_paths: 图片路径列表
            field_title: 字段标题

        Returns:
            是否成功上传
        """
        try:
            logger.info(f"开始处理素材中心上传流程: {len(image_paths)} 张图片")

            # 1. 查找并点击素材中心按钮
            material_center_clicked = self._click_material_center()
            if not material_center_clicked:
                logger.error("未找到或无法点击素材中心按钮")
                return False

            # 2. 等待素材中心界面加载
            self.tab.wait(3)

            # 3. 上传图片到素材中心
            uploaded_count = self._upload_to_material_center(image_paths)
            if uploaded_count == 0:
                logger.error("没有成功上传任何图片到素材中心")
                return False

            # 4. 选择上传的图片
            selected_count = self._select_uploaded_images(uploaded_count)
            if selected_count == 0:
                logger.error("没有成功选择任何图片")
                return False

            # 5. 确认选择
            confirm_success = self._confirm_image_selection()
            if not confirm_success:
                logger.error("确认图片选择失败")
                return False

            logger.info(f"素材中心上传流程完成: 上传 {uploaded_count} 张，选择 {selected_count} 张")
            return True

        except Exception as e:
            logger.error(f"素材中心上传流程失败: {str(e)}")
            return False

    def _click_material_center(self) -> bool:
        """
        点击素材中心按钮

        Returns:
            是否成功点击
        """
        # 尝试多种选择器查找素材中心按钮
        material_center_selectors = [
            "x://button[contains(text(), '素材中心')]",
            "x://div[contains(text(), '素材中心')]",
            "x://span[contains(text(), '素材中心')]",
            "x://*[contains(text(), '素材中心')]",
            "x://button[contains(@class, 'material')]",
            "x://div[contains(@class, 'material')]"
        ]

        for selector in material_center_selectors:
            try:
                element = self.tab.ele(selector, timeout=3)
                if element and element.states.is_displayed:
                    logger.info(f"找到素材中心按钮: {selector}")
                    element.click()
                    return True
            except Exception as e:
                logger.debug(f"素材中心按钮查找失败: {selector} -> {str(e)}")
                continue

        return False

    def _upload_to_material_center(self, image_paths: list[str]) -> int:
        """
        上传图片到素材中心

        Args:
            image_paths: 图片路径列表

        Returns:
            成功上传的图片数量
        """
        uploaded_count = 0

        try:
            logger.info(f"准备上传 {len(image_paths)} 张图片到素材中心")

            # 查找文件输入框（隐藏的input[type=file]）
            file_input = self._find_file_input()
            if file_input:
                # 批量上传所有图片
                try:
                    # 将所有图片路径合并为一个字符串，用换行符分隔
                    all_paths = '\n'.join(image_paths)
                    file_input.input(all_paths)
                    uploaded_count = len(image_paths)
                    logger.info(f"批量上传 {uploaded_count} 张图片成功")

                    # 等待上传完成
                    self.tab.wait(5)
                    return uploaded_count
                except Exception as e:
                    logger.error(f"批量上传失败: {str(e)}")

            # 如果批量上传失败，尝试逐个上传
            upload_button = self._find_upload_button()
            if not upload_button:
                logger.error("未找到上传按钮")
                return 0

            for i, image_path in enumerate(image_paths):
                try:
                    # 点击上传按钮
                    upload_button.click()
                    self.tab.wait(1)

                    # 查找文件输入框
                    file_input = self._find_file_input()
                    if file_input:
                        file_input.input(image_path)
                        uploaded_count += 1
                        logger.info(f"上传第 {i + 1} 张图片: {os.path.basename(image_path)}")
                        self.tab.wait(3)  # 等待上传完成
                    else:
                        logger.error(f"第 {i + 1} 张图片上传失败：未找到文件输入框")

                except Exception as e:
                    logger.error(f"上传第 {i + 1} 张图片失败: {str(e)}")
                    continue

            return uploaded_count

        except Exception as e:
            logger.error(f"素材中心上传失败: {str(e)}")
            return 0

    def _find_file_input(self):
        """查找文件输入框"""
        file_input_selectors = [
            "x://input[@type='file']",
            "x://input[@accept]",
            "x://input[contains(@accept, 'image')]"
        ]

        for selector in file_input_selectors:
            try:
                element = self.tab.ele(selector, timeout=2)
                if element:
                    return element
            except Exception:
                continue
        return None

    def _find_upload_button(self):
        """查找上传按钮"""
        upload_selectors = [
            "x://button[contains(text(), '上传')]",
            "x://div[contains(text(), '上传')]",
            "x://span[contains(text(), '上传')]",
            "x://button[contains(text(), '添加')]",
            "x://div[contains(@class, 'upload')]",
            "x://*[contains(text(), '选择文件')]"
        ]

        for selector in upload_selectors:
            try:
                element = self.tab.ele(selector, timeout=2)
                if element and element.states.is_displayed:
                    logger.info(f"找到上传按钮: {selector}")
                    return element
            except Exception:
                continue
        return None

    def _select_uploaded_images(self, count: int) -> int:
        """
        选择已上传的图片

        Args:
            count: 要选择的图片数量

        Returns:
            成功选择的图片数量
        """
        selected_count = 0

        try:
            # 等待图片加载完成
            logger.info("等待图片加载完成...")
            self.tab.wait(5)

            # 查找最新上传的图片（通常在第一页的前几张）
            logger.info("开始选择最新上传的图片...")

            # 查找图片容器
            image_containers = self._find_image_containers()
            if not image_containers:
                logger.error("未找到图片容器")
                return 0

            logger.info(f"找到 {len(image_containers)} 个图片容器")

            # 选择前N个图片容器
            for i in range(min(count, len(image_containers))):
                try:
                    container = image_containers[i]
                    success = self._select_image_container(container, i + 1)
                    if success:
                        selected_count += 1
                        self.tab.wait(0.5)
                except Exception as e:
                    logger.error(f"选择第 {i + 1} 张图片失败: {str(e)}")
                    continue

            logger.info(f"成功选择 {selected_count} 张图片")
            return selected_count

        except Exception as e:
            logger.error(f"选择图片失败: {str(e)}")
            return 0

    def _find_image_containers(self):
        """查找图片容器"""
        container_selectors = [
            "x://div[contains(@class, 'image-item')]",
            "x://div[contains(@class, 'photo-item')]",
            "x://div[contains(@class, 'picture-item')]",
            "x://li[contains(@class, 'item')]",
            "x://div[.//img and contains(@class, 'item')]",
            "x://div[.//img]"
        ]

        for selector in container_selectors:
            try:
                containers = self.tab.eles(selector, timeout=3)
                if containers and len(containers) > 0:
                    logger.info(f"找到 {len(containers)} 个图片容器: {selector}")
                    return containers
            except Exception:
                continue

        # 如果没找到容器，直接查找图片
        try:
            images = self.tab.eles("x://img", timeout=3)
            if images:
                logger.info(f"直接找到 {len(images)} 张图片")
                return images
        except Exception:
            pass

        return []

    def _select_image_container(self, container, index: int) -> bool:
        """选择图片容器"""
        try:
            # 尝试多种选择方式

            # 1. 查找容器内的复选框
            try:
                checkbox = container.ele(".//input[@type='checkbox']", timeout=1)
                if checkbox:
                    checkbox.click()
                    logger.info(f"通过复选框选择第 {index} 张图片")
                    return True
            except Exception:
                pass

            # 2. 直接点击容器
            try:
                container.click()
                logger.info(f"通过点击容器选择第 {index} 张图片")
                return True
            except Exception:
                pass

            # 3. 点击容器内的图片
            try:
                img = container.ele(".//img", timeout=1)
                if img:
                    img.click()
                    logger.info(f"通过点击图片选择第 {index} 张图片")
                    return True
            except Exception:
                pass

            # 4. 查找选择按钮
            try:
                select_btn = container.ele(".//*[contains(text(), '选择') or contains(text(), '选中')]", timeout=1)
                if select_btn:
                    select_btn.click()
                    logger.info(f"通过选择按钮选择第 {index} 张图片")
                    return True
            except Exception:
                pass

            return False

        except Exception as e:
            logger.error(f"选择第 {index} 张图片失败: {str(e)}")
            return False

    def _try_select_all(self) -> bool:
        """
        尝试使用全选功能

        Returns:
            是否成功使用全选
        """
        select_all_selectors = [
            "x://button[contains(text(), '全选')]",
            "x://span[contains(text(), '全选')]",
            "x://div[contains(text(), '全选')]",
            "x://input[@type='checkbox' and contains(@class, 'select-all')]",
            "x://*[contains(@class, 'select-all')]"
        ]

        for selector in select_all_selectors:
            try:
                element = self.tab.ele(selector, timeout=2)
                if element and element.states.is_displayed:
                    logger.info(f"找到全选按钮: {selector}")
                    element.click()
                    self.tab.wait(1)
                    return True
            except Exception:
                continue

        return False

    def _click_image_for_selection(self, image_element, index: int) -> bool:
        """
        点击图片进行选择

        Args:
            image_element: 图片元素
            index: 图片索引

        Returns:
            是否成功选择
        """
        try:
            # 尝试直接点击图片
            image_element.click()
            logger.info(f"选择第 {index} 张图片")
            return True
        except Exception:
            try:
                # 尝试点击图片的父容器
                parent = image_element.parent()
                if parent:
                    parent.click()
                    logger.info(f"通过父容器选择第 {index} 张图片")
                    return True
            except Exception:
                try:
                    # 尝试查找选择框
                    checkbox = image_element.parent().ele(".//input[@type='checkbox']", timeout=1)
                    if checkbox:
                        checkbox.click()
                        logger.info(f"通过复选框选择第 {index} 张图片")
                        return True
                except Exception:
                    pass

        return False

    def _confirm_image_selection(self) -> bool:
        """
        确认图片选择

        Returns:
            是否成功确认
        """
        try:
            logger.info("开始查找确认按钮...")

            # 等待一下确保页面稳定
            self.tab.wait(2)

            # 查找确认按钮，使用更多的选择器
            confirm_selectors = [
                # 中文确认按钮
                "x://button[contains(text(), '确认')]",
                "x://button[contains(text(), '确定')]",
                "x://button[contains(text(), '完成')]",
                "x://button[contains(text(), '保存')]",
                "x://span[contains(text(), '确认')]",
                "x://div[contains(text(), '确认')]",

                # 英文确认按钮
                "x://button[contains(text(), 'Confirm')]",
                "x://button[contains(text(), 'OK')]",
                "x://button[contains(text(), 'Save')]",
                "x://button[contains(text(), 'Done')]",

                # 通过类名查找
                "x://button[contains(@class, 'confirm')]",
                "x://button[contains(@class, 'ok')]",
                "x://button[contains(@class, 'primary')]",
                "x://button[contains(@class, 'submit')]",

                # 通用按钮（在弹窗底部）
                "x://div[contains(@class, 'modal')]//button[last()]",
                "x://div[contains(@class, 'dialog')]//button[last()]",
                "x://div[contains(@class, 'footer')]//button",
                "x://div[contains(@class, 'bottom')]//button",

                # 任何可见的按钮（作为最后的尝试）
                "x://button[@type='submit']",
                "x://button[not(@disabled)]"
            ]

            for selector in confirm_selectors:
                try:
                    elements = self.tab.eles(selector, timeout=2)
                    for element in elements:
                        if element and element.states.is_displayed and element.states.is_enabled:
                            # 检查按钮文本是否合理
                            text = element.text.strip()
                            if text and not any(skip in text.lower() for skip in ['取消', 'cancel', '关闭', 'close']):
                                logger.info(f"找到确认按钮: {selector} - 文本: '{text}'")
                                element.click()
                                self.tab.wait(3)  # 等待确认完成
                                return True
                except Exception as e:
                    logger.debug(f"确认按钮查找失败: {selector} -> {str(e)}")
                    continue

            # 如果还是没找到，尝试按回车键
            try:
                logger.info("尝试按回车键确认")
                self.tab.key.enter()
                self.tab.wait(2)
                return True
            except Exception:
                pass

            logger.error("未找到确认按钮")
            return False

        except Exception as e:
            logger.error(f"确认图片选择失败: {str(e)}")
            return False

    def find_field_container(self, field_title: str):
        """
        根据字段标题查找字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        # 尝试多种方式查找字段容器
        selectors = [
            "x://div[.='商品轮播图']/..//div[@data-testid='beast-core-form-item']//div[.='素材中心']",
        ]

        for selector in selectors:
            element = self.wait_for_element(selector, timeout=3)
            if element:
                logger.debug(f"找到字段容器: {field_title} -> {selector}")
                return element

        logger.warning(f"未找到字段容器: {field_title}")
        return None
