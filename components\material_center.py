#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
素材中心组件

主要功能：
1. 打开素材中心弹窗
2. 自动化上传图片（绕过系统文件选择框）
3. 选择已上传的图片
4. 确认选择并关闭弹窗
"""

import os
import time
import requests
from pathlib import Path
from typing import List, Optional, Union, Dict, Any
from loguru import logger

from components.base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class MaterialCenterComponent(BaseComponent):
    """素材中心组件"""


    def __init__(self, tab):
        """
        初始化素材中心组件
        
        Args:
            tab: 浏览器标签页对象
        """
        super().__init__(tab)
        self.component_name = "MaterialCenter"

    def is_opened(self) -> bool:
        """
        判断是否已打开

        Returns:
            bool: 是否已打开
        """
        try:
            ele = self.tab.ele("x://span[.='进入素材中心']", timeout=3)
            if not ele:
                return False

            return True

        except Exception as e:
            logger.error(e)
            return False

    def _close_upload_list_view(self) -> ApiResponse:
        """
        关闭上传列表视图

        Returns:
            ApiResponse: 关闭结果
        """
        try:
            logger.info("正在关闭上传列表视图...")
            _ele = self.tab.ele("x://header[.='上传列表']", timeout=2)
            if _ele:
                self.tab.ele("x://a[.='上传列表']").click()
                has_ele = self.tab.ele("x://header[.='上传列表']", timeout=2)
                if not has_ele:
                    logger.warning("上传列表视图关闭失败")
                    return error_response("上传列表视图关闭失败")

                return success_response("上传列表视图已关闭")
            else:
                logger.warning("未找到上传列表视图关闭按钮")
                return success_response("上传列表视图可能已关闭")
        except Exception as e:
            logger.error(f"关闭上传列表视图失败: {str(e)}")
            return error_response(f"关闭上传列表视图失败: {str(e)}")

    def upload_images(self, image_paths: Union[str, List[str]]) -> ApiResponse:
        """
        上传图片到素材中心

        Args:
            image_paths: 图片路径，可以是单个路径字符串或路径列表

        Returns:
            ApiResponse: 上传结果
        """
        try:
            # 处理和验证图片路径
            valid_paths, upload_image_names = self._prepare_image_paths(image_paths)
            if not valid_paths:
                return error_response("没有有效的图片文件可上传")

            logger.debug(f"有效图片文件: {len(valid_paths)}/{len(image_paths) if isinstance(image_paths, list) else 1}")

            # 执行批量上传
            return self._execute_batch_upload(valid_paths, upload_image_names, len(image_paths) if isinstance(image_paths, list) else 1)

        except Exception as e:
            return error_response(f"上传图片失败: {str(e)}")

    def _prepare_image_paths(self, image_paths: Union[str, List[str]]) -> tuple[List[str], List[str]]:
        """准备和验证图片路径"""
        # 处理路径参数
        if isinstance(image_paths, str):
            if os.path.isdir(image_paths):
                image_paths = self._get_images_from_directory(image_paths)
            else:
                image_paths = [image_paths]

        if not image_paths:
            return [], []

        logger.debug(f"准备批量上传 {len(image_paths)} 张图片")

        # 验证所有图片文件
        valid_paths = []
        upload_image_names = []
        for image_path in image_paths:
            if os.path.exists(image_path):
                file_size = os.path.getsize(image_path)
                if file_size <= 10 * 1024 * 1024:  # 10MB限制
                    valid_paths.append(image_path)
                    image_name = os.path.basename(image_path).split('.')[0]
                    upload_image_names.append(image_name)
                    logger.debug(f"验证通过: {os.path.basename(image_path)} ({file_size / 1024:.1f}KB)")
                else:
                    logger.warning(f"文件过大跳过: {os.path.basename(image_path)} ({file_size / 1024 / 1024:.1f}MB)")
            else:
                logger.warning(f"文件不存在跳过: {image_path}")

        return valid_paths, upload_image_names

    def _execute_batch_upload(self, valid_paths: List[str], upload_image_names: List[str], total_count: int) -> ApiResponse:
        """执行批量上传"""
        try:
            logger.debug("开始批量上传图片...")

            # 启动上传流程
            self.tab.listen.start("api/material/query-md5")
            a_ele = self.tab.ele("x://a[.='本地上传']")
            self.tab.set.upload_files(valid_paths)
            a_ele.click()
            self.tab.wait.upload_paths_inputted()

            # 处理MD5校验响应
            upload_image_names = self._handle_md5_response(upload_image_names, valid_paths)
            if upload_image_names is None:  # 所有图片都已存在
                return success_response(f"所有图片都已存在, 无需上传",
                                      data={"uploaded_count": len(valid_paths), "total_count": total_count})

            # 等待上传完成并验证
            result = self.tab.wait.ele_displayed("x://div[contains(@class, 'TB_emptyData')]", timeout=30)
            logger.info(f"TB_emptyData : {result}")

            upload_success = self._verify_batch_upload_complete(upload_image_names)
            if upload_success:
                logger.info(f"✅ 批量上传成功: {len(valid_paths)} 张图片")
                self._close_upload_list_view()
                return success_response(f"批量上传成功: {len(valid_paths)} 张图片",
                                      data={"uploaded_count": len(valid_paths), "total_count": total_count})

            return error_response("批量上传图片失败")
        except Exception as e:
            logger.error(f"批量上传失败: {str(e)}")
            return error_response("批量上传图片失败")

    def _handle_md5_response(self, upload_image_names: List[str], valid_paths: List[str]) -> List[str]:
        """处理MD5校验响应，返回需要上传的图片名称列表，如果所有图片都已存在则返回None"""
        res = self.tab.listen.wait(timeout=120)
        self.tab.listen.start("material/page-query-material")
        response_data = res.response.body

        if not response_data:
            logger.error("上传图片失败, 校验图片md5无响应, 请检查网络")
            raise Exception("校验md5无响应, 请检查网络")

        result = response_data.get("result")
        if not result:
            raise Exception("校验md5响应异常")

        response_detail_list = result.get("responseDetailList", [])
        if len(response_detail_list) > 0:
            material_names = [detail.get("materialName", "")
                            for detail in response_detail_list
                            if detail.get("alreadyExists", False)]

            if material_names:
                logger.warning(f"⚠️ 图片已存在: {', '.join(material_names)}")
                self.tab.ele("x://button[@data-testid='beast-core-modal-close-button']").click()

                upload_image_names = list(set(upload_image_names) - set(material_names))
                if len(material_names) == len(valid_paths):
                    logger.debug("所有图片都已存在, 无需上传")
                    return None

        return upload_image_names
    
    def select_images(self, image_names: Union[str, List[str]]) -> ApiResponse:
        """
        选择已上传的图片
        
        Args:
            image_names: 要选择的图片名列表
            
        Returns:
            ApiResponse: 选择结果
        """
        try:
            image_count = len(image_names)
            logger.debug(f"正在选择 {image_count} 张图片...")

            # 等待图片加载
            time.sleep(3)
            selected_count = 0
            for image_name in image_names:
                # 查找图片容器
                container = self.tab.ele(f"x://div[contains(@class, 'index-module_cardContainer') and .//div[contains(text(), '{image_name}')]]", timeout=2)
                if not container:
                    logger.warning(f"未找到图片容器: {image_name}")
                    continue

                container.click()
                selected_count = selected_count + 1
                time.sleep(0.5)

            if selected_count == 0:
                return error_response("未能选择任何图片")
            
            logger.debug(f"已选择 {selected_count} 张图片")
            return success_response(
                f"已选择 {selected_count} 张图片",
                data={"selected_count": selected_count}
            )
            
        except Exception as e:
            return error_response(f"选择图片失败: {str(e)}")
    
    def confirm_selection(self) -> ApiResponse:
        """
        确认选择并关闭素材中心弹窗
        
        Returns:
            ApiResponse: 确认结果
        """
        try:
            logger.debug("正在确认选择...")
            confirm_btn = self.tab.ele("x://button[.='确认' and @data-testid='beast-core-button']", timeout=2)
            if not confirm_btn:
                return error_response("未找到确认按钮")
            
            # 点击确认按钮
            confirm_btn.click()
            self.tab.wait(2)
            
            # 验证弹窗是否关闭
            time.sleep(1)
            modal_still_open = self.is_opened()
            if modal_still_open:
                logger.warning("素材中心弹窗可能仍然打开")
                return success_response("已点击确认按钮，但弹窗可能仍然打开")
            else:
                logger.debug("素材中心弹窗已关闭")
                return success_response("已确认选择并关闭弹窗")
                
        except Exception as e:
            return error_response(f"确认选择失败: {str(e)}")

    
    def complete_upload_flow(self, image_paths: Union[str, List[str]], 
                           select_count: int = 5) -> ApiResponse:
        """
        完整的上传流程：打开素材中心 -> 上传图片 -> 选择图片 -> 确认选择
        
        Args:
            image_paths: 图片路径
            select_count: 要选择的图片数量
            
        Returns:
            ApiResponse: 完整流程结果
        """
        try:
            logger.debug("开始执行完整的图片上传流程...")

            # 1. 打开素材中心
            result = self.is_opened()
            if not result:
                return error_response("未找到素材中心按钮")

            # 2. 上传图片
            result = self.upload_images(image_paths)
            if not result.is_success():
                return result

            # 3. 选择图片
            image_names = [os.path.basename(path).split('.')[0] for path in image_paths]
            result = self.select_images(image_names)
            if not result.is_success():
                return result

            # 4. 确认选择
            result = self.confirm_selection()
            if not result.is_success():
                return result

            logger.debug("完整的图片上传流程执行成功")
            return success_response(
                "图片上传流程完成",
                data={"selected_count": select_count}
            )

        except Exception as e:
            logger.error(f"完整上传流程失败: {str(e)}")
            return error_response(f"完整上传流程失败: {str(e)}")
    
    def _find_file_input(self):
        """查找文件上传输入框"""
        file_input = self.tab.ele("x://div[@data-testid='beast-core-modal-inner']//input[@type='file']", timeout=2)
        if file_input:
            logger.debug("找到文件上传输入框")
            return file_input
        
        logger.warning("未找到文件上传输入框")
        return None


    def _upload_single_image(self, file_input, image_path: str) -> bool:
        """
        上传单张图片（需要传入文件输入框）

        Args:
            file_input: 文件输入框元素
            image_path: 图片路径

        Returns:
            bool: 是否上传成功
        """
        try:
            # 验证文件是否存在
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                return False

            # 验证文件大小（避免上传过大文件）
            file_size = os.path.getsize(image_path)
            if file_size > 10 * 1024 * 1024:  # 10MB限制
                logger.error(f"图片文件过大: {image_path} ({file_size / 1024 / 1024:.1f}MB)")
                return False

            self.tab.listen.start("api/material/query-md5")
            logger.debug(f"开始上传图片: {os.path.basename(image_path)} ({file_size / 1024:.1f}KB)")

            a_ele = self.tab.ele("x://a[.='本地上传']")

            # 设置要上传的文件路径
            self.tab.set.upload_files(image_path)
            # 点击触发文件选择框按钮
            a_ele.click()
            # 等待路径填入
            self.tab.wait.upload_paths_inputted()

            # 等待上传开始的指示器
            time.sleep(1)

            res = self.tab.listen.wait(timeout=120)
            response_data = res.response.body
            if not response_data:
                logger.error(f"上传图片失败: {image_path}, 数据无响应")
                return False

            response_detail_list = response_data.get("result", {}).get("responseDetailList")
            if len(response_detail_list) > 0:
                material_names = []
                for detail in response_detail_list:
                    already_exists = detail.get("alreadyExists", False)
                    material_name = detail.get("materialName", "")
                    if already_exists:
                        material_names.append(material_name)

                logger.warning(f"图片已存在: {', '.join(material_names)} ")
                self.tab.ele("x://button[@data-testid='beast-core-modal-close-button']").click()
                return True

            # 检查是否有上传进度或成功指示器
            upload_success = self._wait_for_upload_complete(image_path)

            if upload_success:
                logger.debug(f"图片上传成功: {os.path.basename(image_path)}")
                return True
            else:
                logger.warning(f"图片上传可能失败或超时: {os.path.basename(image_path)}")
                return False

        except Exception as e:
            logger.error(f"上传图片失败 {image_path}: {str(e)}")
            return False

    def _upload_single_image_auto(self, image_path: str) -> bool:
        """
        上传单张图片（自动查找文件输入框）

        Args:
            image_path: 图片路径

        Returns:
            bool: 是否上传成功
        """
        try:
            # 自动查找文件输入框
            file_input = self._find_file_input()
            if not file_input:
                logger.error(f"未找到文件输入框，无法上传: {os.path.basename(image_path)}")
                return False

            # 调用原有的上传方法
            return self._upload_single_image(file_input, image_path)

        except Exception as e:
            logger.error(f"自动上传图片失败 {image_path}: {str(e)}")
            return False

    def _wait_for_upload_complete(self, image_path: str, timeout: int = 10) -> bool:
        """
        等待图片上传完成

        Args:
            image_path: 图片路径
            timeout: 超时时间（秒）

        Returns:
            bool: 是否上传成功
        """
        try:
            image_name = os.path.basename(image_path)
            start_time = time.time()

            while time.time() - start_time < timeout:
                # 检查是否有上传成功的指示器
                success_indicators = [
                    "x://*[contains(text(), '上传成功')]",
                    "x://*[contains(@class, 'upload-success')]",
                    "x://*[contains(@class, 'success')]",
                    # 可以根据实际页面添加更多指示器
                ]

                for indicator in success_indicators:
                    if self.tab.ele(indicator, timeout=0.5):
                        logger.debug(f"检测到上传成功指示器: {indicator}")
                        return True

                # 检查是否有错误指示器
                error_indicators = [
                    "x://*[contains(text(), '上传失败')]",
                    "x://*[contains(text(), '错误')]",
                    "x://*[contains(@class, 'upload-error')]",
                    "x://*[contains(@class, 'error')]",
                ]

                for indicator in error_indicators:
                    if self.tab.ele(indicator, timeout=0.5):
                        logger.warning(f"检测到上传错误指示器: {indicator}")
                        return False

                time.sleep(0.5)

            # 超时后假设上传成功（因为有些页面可能没有明确的成功指示器）
            logger.debug(f"上传等待超时，假设成功: {image_name}")
            return True

        except Exception as e:
            logger.debug(f"等待上传完成时出错: {str(e)}")
            return True  # 出错时假设成功
    
    def _get_images_from_directory(self, directory: str) -> List[str]:
        """
        从目录获取所有图片文件
        
        Args:
            directory: 目录路径
            
        Returns:
            List[str]: 图片文件路径列表
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        image_paths = []
        
        try:
            directory_path = Path(directory)
            if not directory_path.exists():
                logger.error(f"目录不存在: {directory}")
                return []
            
            for file_path in directory_path.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                    image_paths.append(str(file_path.absolute()))
            
            # 按文件名排序
            image_paths.sort()
            logger.info(f"从目录 {directory} 找到 {len(image_paths)} 张图片")
            
        except Exception as e:
            logger.error(f"读取目录失败 {directory}: {str(e)}")
        
        return image_paths

    def upload_images_individually(self, image_paths: List[str]) -> ApiResponse:
        """
        逐个上传图片（备用方案）

        Args:
            image_paths: 图片路径列表

        Returns:
            ApiResponse: 上传结果
        """
        try:
            logger.info(f"开始逐个上传 {len(image_paths)} 张图片")

            uploaded_count = 0
            for i, image_path in enumerate(image_paths):
                logger.info(f"正在上传第 {i+1}/{len(image_paths)} 张图片: {os.path.basename(image_path)}")

                # 每次上传前都重新查找文件输入框
                file_input = self._find_file_input()
                if not file_input:
                    logger.warning(f"第 {i+1} 张图片上传失败: 未找到文件输入框")
                    continue

                # 上传单张图片
                if self._upload_single_image(file_input, image_path):
                    uploaded_count += 1
                    logger.info(f"✅ 已上传: {os.path.basename(image_path)} ({uploaded_count}/{len(image_paths)})")

                    # 等待上传处理完成
                    time.sleep(2)
                else:
                    logger.warning(f"❌ 上传失败: {os.path.basename(image_path)}")

                # 避免上传过快，给页面一些处理时间
                if i < len(image_paths) - 1:  # 不是最后一张图片
                    time.sleep(1)

            # 返回上传结果
            if uploaded_count == 0:
                return error_response("所有图片上传失败")
            elif uploaded_count < len(image_paths):
                return success_response(
                    f"部分图片上传成功: {uploaded_count}/{len(image_paths)}",
                    data={"uploaded_count": uploaded_count, "total_count": len(image_paths)}
                )
            else:
                return success_response(
                    f"所有图片上传成功: {uploaded_count}张",
                    data={"uploaded_count": uploaded_count}
                )

        except Exception as e:
            return error_response(f"逐个上传图片失败: {str(e)}")


    @staticmethod
    def _make_request(url, method, headers):
        """
        发起请求，支持代理
        Args:
            url: 请求url
            method: GET/POST/PUT/DELETE
            headers: HTTP headers

        Returns:
            list: 素材列表
        """

        # 过滤掉 HTTP/2 伪头部字段（以 : 开头的字段）
        filtered_headers = {}
        for key, value in headers.items():
            if not key.startswith(':'):
                filtered_headers[key] = value

        logger.debug(f"原始headers数量: {len(headers)}, 过滤后headers数量: {len(filtered_headers)}")

        data = "{\"pageInfo\":{\"pageNo\":1,\"pageSize\":50},\"uploadStatusList\":[3],\"folderId\":0,\"materialTypeList\":[1],\"minMaterialHeight\":800,\"minMaterialWidth\":800,\"maxMaterialSize\":2097152,\"proportion\":\"1:1\",\"proportionList\":[\"1:1\"],\"badTagList\":[\"ZH_HANS\"]}"

        for attempt in range(3):
            try:
                logger.debug(f"第 {attempt + 1} 次请求尝试")
                logger.debug(f"请求URL: {url}")
                logger.debug(f"请求方法: {method}")

                response = requests.request(
                    method=method,
                    url=url,
                    data=data,
                    headers=filtered_headers,
                    timeout=30
                )

                logger.info(f"请求状态码: {response.status_code}")

                if response.status_code == 200:
                    response_data = response.json()

                    result = response_data.get("result", {})
                    if result:
                        material_list = result.get("materialList", [])
                        # logger.success(f"成功获取素材列表: {len(material_list)} 个素材")
                        return material_list
                    else:
                        logger.warning(f"API返回错误码: {response_data.get('code')}, 消息: {response_data.get('message', 'unknown')}")
                else:
                    logger.warning(f"HTTP状态码异常: {response.status_code}")
                    logger.debug(f"响应内容: {response.text[:200]}...")

            except requests.exceptions.Timeout:
                logger.error(f"第 {attempt + 1} 次请求超时")
            except requests.exceptions.RequestException as e:
                logger.error(f"第 {attempt + 1} 次请求异常: {e}")
            except Exception as e:
                logger.error(f"第 {attempt + 1} 次请求失败: {e}")

            if attempt < 2:  # 不是最后一次尝试
                logger.info(f"等待 2 秒后重试...")
                import time
                time.sleep(2)

        logger.error("所有请求尝试均失败")
        return []

    def _verify_batch_upload_complete(self, upload_image_names: List[str], timeout: int = 30) -> bool:
        """
        验证批量上传是否完成
        通过监听网络请求并匹配返回的素材列表来确认上传成功

        Args:
            upload_image_names: 上传的图片名称列表
            timeout: 超时时间（秒）

        Returns:
            bool: 是否上传成功
        """
        try:
            logger.debug(f"验证批量上传完成，期望图片: {upload_image_names}")
            expected_count = len(upload_image_names)

            # 监听网络请求
            logger.info("开始监听素材查询请求...")
            res = self.tab.listen.wait(timeout=timeout)

            if not res:
                logger.warning("监听超时，未捕获到素材查询请求")
                return False

            url = res.response.url
            if "page-query-material" not in url:
                logger.error(f"监听的url出现错乱 url={url}")
                return False

            logger.debug(f"捕获到素材查询请求: {url}")

            # 获取请求信息
            headers = res.request.headers
            request_url = res.request.url
            method = res.request.method

            for attempt in range(5):
                # 发起请求获取素材列表
                self.tab.wait(3)
                logger.debug(f"{attempt + 1} 发起请求...")

                material_list = self._make_request(request_url, method, headers)

                if not material_list:
                    logger.warning("获取素材列表为空")
                    self.tab.wait(3)
                    continue

                logger.success(f"获取到 {len(material_list)} 个素材")

                # 匹配上传的图片名称
                matched_materials = []
                uploaded_material_names = []

                for material in material_list:
                    material_name = material.get("materialName", "")
                    upload_status = material.get("uploadStatus", 0)
                    material_id = material.get("id", "")
                    img_url = material.get("imgUrl", "")

                    # 记录所有已上传的素材名称
                    if upload_status == 3:  # uploadStatus=3 表示上传成功
                        uploaded_material_names.append(material_name)

                        # 检查是否匹配我们上传的图片名称
                        for upload_name in upload_image_names:
                            # 支持多种匹配方式
                            if (upload_name in material_name or
                                material_name in upload_name or
                                upload_name.replace('_', '').replace('-', '') in material_name.replace('_', '').replace('-', '')):

                                matched_materials.append({
                                    "upload_name": upload_name,
                                    "material_name": material_name,
                                    "material_id": material_id,
                                    "img_url": img_url,
                                    "upload_status": upload_status
                                })
                                logger.debug(f"匹配成功: {upload_name} -> {material_name}")
                                break

                # 统计匹配结果
                matched_count = len(matched_materials)
                if matched_count == 0:
                    logger.warning(f"未匹配到结果...")
                    self.tab.wait(5)
                    continue

                success_rate = matched_count / expected_count if expected_count > 0 else 0

                logger.info(f"素材匹配结果: {matched_count}/{expected_count} ({success_rate*100:.1f}%)")

                # 详细日志
                if matched_materials:
                    logger.info("匹配成功的素材:")
                    for material in matched_materials:
                        logger.info(f"  - {material['upload_name']} -> {material['material_name']} (ID: {material['material_id']})")

                # 显示未匹配的图片名称
                unmatched_names = []
                for upload_name in upload_image_names:
                    if not any(m['upload_name'] == upload_name for m in matched_materials):
                        unmatched_names.append(upload_name)

                if unmatched_names:
                    logger.warning(f"未匹配的图片名称: {unmatched_names}")
                    logger.debug(f"已上传的素材名称: {uploaded_material_names[:10]}...")  # 只显示前10个

                # 判断成功条件
                success_threshold = max(1, int(expected_count * 1))  # 100%成功率阈值

                if matched_count >= expected_count:
                    logger.success(f"✅ 批量上传完全成功: {matched_count}/{expected_count}")
                    return True
                else:
                    logger.warning(f"❌ 批量上传成功率不足: {matched_count}/{expected_count} (低于阈值 {success_threshold})")
                    continue

        except Exception as e:
            logger.error(f"验证批量上传完成时出错: {str(e)}")
            return False

    def find_field_container(self, field_title: str):
        pass

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        pass
