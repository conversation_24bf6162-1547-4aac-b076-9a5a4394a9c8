"""
文件上传组件

处理文件上传字段，支持批量上传功能
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Union

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class UploadInputComponent(BaseComponent):
    """文件上传组件"""

    def find_field_container(self, field_title: str):
        """
        查找字段容器

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        try:
            return self.tab.ele("x://span[.='批量上传']/..", timeout=3)
        except Exception as e:
            logger.error(f"查找字段容器失败: {field_title} - {str(e)}")
            return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充文件上传字段，使用DrissionPage的上传功能

        Args:
            field_title: 字段标题
            field_data: 字段数据

        Returns:
            ApiResponse: 操作结果
        """
        try:
            # 验证必填字段
            validation_error = self.validate_required_field(field_title, field_data)
            if validation_error:
                return validation_error

            value = field_data.get("value")
            if value is None:
                self.log_operation(field_title, "跳过填充（值为空）")
                return success_response(f"字段 '{field_title}' 跳过填充")

            # 验证文件路径
            file_paths = self._get_file_paths(value)
            if not file_paths:
                error_msg = f"未找到有效的文件路径: {value}"
                self.log_operation(field_title, "验证文件路径", False, error_msg)
                return error_response(error_msg)

            # 验证文件有效性
            valid_paths = self._validate_file_paths(file_paths)
            if not valid_paths:
                error_msg = f"没有有效的文件可上传"
                self.log_operation(field_title, "验证文件有效性", False, error_msg)
                return error_response(error_msg)

            # 查找字段容器
            field_container = self.find_field_container(field_title)
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            try:
                self.tab.run_js("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", field_container)
                self.tab.wait(1)  # 等待滚动完成
            except Exception as e:
                pass

            # 开始上传流程
            logger.info(f"📸 {field_title}: 开始上传 {len(valid_paths)} 个文件")
            
            try:
                # 开始监听上传响应
                self.tab.listen.start("store_image?sdk_version")

                # 设置文件上传
                self.tab.set.upload_files(valid_paths)
                self.tab.wait(2)  # 等待弹窗打开
                
                # 点击字段容器触发上传
                field_container.click()

                # 等待路径填入
                self.tab.wait.upload_paths_inputted()
                
                # 等待上传响应，设置合理的超时时间和期望的响应数量
                res = self.tab.listen.wait(timeout=120, count=len(valid_paths))
                
                # 解析上传结果
                urls = []
                for packet in res:
                    try:
                        body = packet.response.body
                        url = body.get("url")
                        if url:
                            urls.append(url)
                    except Exception as e:
                        logger.warning(f"解析上传响应失败: {str(e)}")
                        continue

                # 检查上传结果
                if len(urls) == len(valid_paths):
                    logger.info(f"✅ {field_title}: 上传完成 {len(urls)} 个文件")
                    return success_response(f"成功上传 {len(urls)} 个文件")
                elif len(urls) > 0:
                    # 部分上传成功
                    logger.warning(f"⚠️ {field_title}: 部分上传成功 {len(urls)}/{len(valid_paths)} 个文件")
                    return success_response(f"部分上传成功: {len(urls)}/{len(valid_paths)} 个文件")
                else:
                    # 上传失败
                    logger.error(f"❌ {field_title}: 上传失败，没有收到有效响应")
                    return error_response("上传失败: 没有收到有效的上传响应")
                    
            except Exception as upload_error:
                error_msg = f"上传过程中发生错误: {str(upload_error)}"
                self.log_operation(field_title, "上传文件", False, error_msg)
                return error_response(error_msg)
            
        except Exception as e:
            error_msg = f"填充文件上传字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)
    
    def _find_upload_element(self, field_container, field_title: str):
        """
        在字段容器中查找上传元素

        Args:
            field_container: 字段容器元素
            field_title: 字段标题

        Returns:
            上传元素或None
        """
        try:
            # 尝试多种选择器查找上传元素
            selectors = [
                ".//input[@type='file']",
                ".//button[contains(text(), '上传')]",
                ".//button[contains(text(), '选择文件')]",
                ".//div[contains(@class, 'upload')]",
                ".//div[contains(@class, 'file-upload')]",
                ".//span[contains(text(), '上传')]",
                ".//span[contains(text(), '选择')]",
                "x:.//div[text()='素材中心']",
                "x:.//span[text()='素材中心']"
            ]

            for selector in selectors:
                element = field_container.ele(selector, timeout=2)
                if element:
                    logger.debug(f"找到上传元素: {field_title} -> {selector}")
                    return element

            logger.warning(f"未找到上传元素: {field_title}")
            return None

        except Exception as e:
            logger.error(f"查找上传元素失败: {field_title} - {str(e)}")
            return None

    def _get_file_paths(self, value) -> List[str]:
        """
        获取文件路径列表

        Args:
            value: 文件路径值（可能是字符串、列表或路径对象）

        Returns:
            有效的文件路径列表
        """
        file_paths = []

        if isinstance(value, str):
            # 如果是字符串，检查是否是目录
            path = Path(value)
            if path.is_dir():
                # 如果是目录，获取目录下的所有图片文件
                for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.webp']:
                    file_paths.extend([str(f) for f in path.glob(ext)])
                    file_paths.extend([str(f) for f in path.glob(ext.upper())])
            elif path.is_file():
                # 如果是文件，直接添加
                file_paths.append(str(path))
        elif isinstance(value, (list, tuple)):
            # 如果是列表，逐个处理
            for item in value:
                file_paths.extend(self._get_file_paths(item))
        elif hasattr(value, '__fspath__'):
            # 如果是路径对象
            file_paths.extend(self._get_file_paths(str(value)))

        # 去重处理（解决Windows文件系统大小写不敏感导致的重复问题）
        unique_paths = list(dict.fromkeys(file_paths))  # 保持顺序的去重

        # 过滤出存在的文件
        valid_paths = [path for path in unique_paths if os.path.isfile(path)]

        return valid_paths

    def _validate_file_paths(self, file_paths: List[str]) -> List[str]:
        """
        验证文件路径并过滤有效文件

        Args:
            file_paths: 文件路径列表

        Returns:
            有效的文件路径列表
        """
        valid_paths = []

        for file_path in file_paths:
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                continue

            if not os.path.isfile(file_path):
                logger.warning(f"不是文件: {file_path}")
                continue

            # 检查文件大小（10MB限制）
            file_size = os.path.getsize(file_path)
            if file_size > 10 * 1024 * 1024:
                logger.warning(f"文件过大: {file_path} ({file_size / 1024 / 1024:.1f}MB)")
                continue

            # 检查文件扩展名
            ext = Path(file_path).suffix.lower()
            if ext not in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                logger.warning(f"不支持的文件格式: {file_path}")
                continue

            valid_paths.append(file_path)
            logger.debug(f"验证通过: {os.path.basename(file_path)} ({file_size / 1024:.1f}KB)")

        return valid_paths
