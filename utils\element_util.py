from time import sleep, time

from DrissionPage._elements.chromium_element import ChromiumElement
from DrissionPage._pages.chromium_frame import ChromiumFrame


def wait_for_condition(
    func, timeout: int, interval: float = 1.0
) -> ChromiumElement | ChromiumFrame | list[ChromiumElement] | list[ChromiumFrame] | None:
    """
    通用等待函数，用于等待元素出现或条件满足

    Args:
        func: 需要执行的函数
        timeout: 超时时间(秒)
        interval: 检查间隔时间(秒)

    Returns
    -------
        返回查找到的元素或None
    """
    start_time = time()
    while time() - start_time < timeout:
        result = func()
        if result:
            return result
        sleep(interval)
    return None


def get_element(tab, condition: str, timeout: int = 3) -> ChromiumElement | None:
    """
    获取单个元素.

    Args:
        tab: 浏览器标签页对象
        condition: 元素定位条件
        timeout: 超时时间(秒)，默认3秒

    Returns
    -------
        返回找到的元素，如果未找到返回None
    """
    return wait_for_condition(lambda: tab(condition, timeout=1), timeout)


def get_elements(tab, condition: str, timeout: int = 3) -> list[ChromiumElement]:
    """
    获取多个元素

    Args:
        tab: 浏览器标签页对象
        condition: 元素定位条件
        timeout: 超时时间(秒)，默认3秒

    Returns
    -------
        返回找到的元素列表，如果未找到返回空列表
    """
    return wait_for_condition(lambda: tab.eles(condition, timeout=1), timeout) or []


def get_frame(tab, frame_id: int, timeout: int = 3) -> ChromiumFrame | None:
    """
    获取框架元素

    Args:
        tab: 浏览器标签页对象
        frame_id: 框架ID
        timeout: 超时时间(秒)，默认3秒

    Returns
    -------
        返回找到的框架元素，如果未找到返回None
    """
    return wait_for_condition(lambda: tab.get_frame(frame_id, timeout=1), timeout)



def try_click(tab, xpath, timeout=5, max_attempts=5, id=None):
    """尝试点击元素，包含重试机制.

    Args:
        tab: 浏览器标签页对象
        xpath: 元素的xpath
        timeout: 每次尝试的超时时间
        max_attempts: 最大尝试次数
        id: 浏览器标识（用于日志）
    Returns:
        bool: 是否点击成功
    """
    for attempt in range(max_attempts):
        try:
            button = tab.ele(xpath, timeout=timeout)
            if button.states.is_clickable:
                button.click()
                return True
            else:
                if attempt < max_attempts - 1:
                    sleep(3)
                    continue
        except Exception as e:
            if attempt < max_attempts - 1:
                sleep(3)
                continue
            else:
                print(f"{id} 多次尝试后仍未能点击元素: {xpath}")
                print(f"错误信息: {str(e)}")
    return False