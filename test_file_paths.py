#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件路径获取逻辑
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from components.upload_input import UploadInputComponent


def test_file_path_detection():
    """测试文件路径检测逻辑"""
    print("🧪 测试文件路径检测逻辑")
    print("=" * 50)
    
    # 创建一个模拟的组件实例
    class MockTab:
        pass
    
    component = UploadInputComponent(MockTab())
    
    # 测试不同的路径情况
    test_cases = [
        # 测试目录路径
        ("测试目录路径", "release/config"),
        # 测试不存在的目录
        ("测试不存在目录", "nonexistent_dir"),
        # 测试文件路径
        ("测试文件路径", "config/fileds.json"),
    ]
    
    for test_name, test_path in test_cases:
        print(f"\n📋 {test_name}: {test_path}")
        
        try:
            file_paths = component._get_file_paths(test_path)
            print(f"  📊 找到 {len(file_paths)} 个文件:")
            
            # 检查是否有重复文件
            unique_paths = set(file_paths)
            if len(file_paths) != len(unique_paths):
                print(f"  ⚠️  发现重复文件! 总数: {len(file_paths)}, 唯一: {len(unique_paths)}")
                
                # 找出重复的文件
                from collections import Counter
                path_counts = Counter(file_paths)
                duplicates = {path: count for path, count in path_counts.items() if count > 1}
                
                for dup_path, count in duplicates.items():
                    print(f"    🔄 重复文件: {Path(dup_path).name} (出现 {count} 次)")
            
            # 显示前几个文件
            for i, file_path in enumerate(file_paths[:5]):
                file_name = Path(file_path).name
                print(f"    {i+1}. {file_name}")
            
            if len(file_paths) > 5:
                print(f"    ... 还有 {len(file_paths) - 5} 个文件")
                
        except Exception as e:
            print(f"  ❌ 错误: {e}")


def simulate_duplicate_extension_issue():
    """模拟重复扩展名问题"""
    print("\n🔍 模拟重复扩展名问题")
    print("=" * 50)
    
    # 模拟 _get_file_paths 的逻辑
    def mock_get_file_paths(directory_path):
        """模拟文件路径获取逻辑"""
        file_paths = []
        path = Path(directory_path)
        
        if path.is_dir():
            print(f"📁 扫描目录: {path}")
            
            # 模拟原始逻辑
            for ext in ['*.jpg', '*.jpeg', '*.png']:
                # 小写搜索
                lowercase_files = list(path.glob(ext))
                print(f"  🔍 小写 {ext}: 找到 {len(lowercase_files)} 个文件")
                file_paths.extend([str(f) for f in lowercase_files])
                
                # 大写搜索
                uppercase_files = list(path.glob(ext.upper()))
                print(f"  🔍 大写 {ext.upper()}: 找到 {len(uppercase_files)} 个文件")
                file_paths.extend([str(f) for f in uppercase_files])
        
        return file_paths
    
    # 测试一个可能包含图片的目录
    test_dir = "release"
    if Path(test_dir).exists():
        file_paths = mock_get_file_paths(test_dir)
        print(f"\n📊 总共找到 {len(file_paths)} 个文件路径")
        
        # 检查重复
        unique_paths = set(file_paths)
        if len(file_paths) != len(unique_paths):
            print(f"⚠️  发现重复! 实际文件: {len(unique_paths)}, 路径条目: {len(file_paths)}")
        else:
            print("✅ 没有发现重复文件")


def suggest_fix():
    """建议修复方案"""
    print("\n💡 修复建议")
    print("=" * 50)
    
    print("问题原因:")
    print("  1. 对每种扩展名进行了小写和大写两次搜索")
    print("  2. 在Windows等不区分大小写的文件系统中，同一文件被找到两次")
    print("  3. 导致文件列表中出现重复项")
    
    print("\n修复方案:")
    print("  1. 使用 set() 去重文件路径")
    print("  2. 或者只搜索一次，然后进行大小写不敏感的匹配")
    print("  3. 在文件路径列表最后进行去重处理")
    
    print("\n建议的代码修改:")
    print("""
    # 修改前
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.webp']:
        file_paths.extend([str(f) for f in path.glob(ext)])
        file_paths.extend([str(f) for f in path.glob(ext.upper())])
    
    # 修改后
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.webp']:
        file_paths.extend([str(f) for f in path.glob(ext)])
        file_paths.extend([str(f) for f in path.glob(ext.upper())])
    
    # 去重处理
    file_paths = list(set(file_paths))
    """)


if __name__ == '__main__':
    test_file_path_detection()
    simulate_duplicate_extension_issue()
    suggest_fix()
