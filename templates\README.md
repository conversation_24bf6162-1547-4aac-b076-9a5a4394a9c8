# Temu自动化工具 - 商品目录模板

## 📁 模板说明

这个目录包含了Temu自动化工具所需的标准商品目录结构模板。

## 🎯 使用方法

1. **复制模板目录**
   ```
   将 goods_template 文件夹复制到您的工作目录
   例如：复制到 D:\temu\goods\
   ```

2. **修改分类目录名**
   ```
   将 "马桶贴" 文件夹重命名为您实际的商品分类名称
   分类名称必须与Temu平台中的分类名称完全一致
   ```

3. **替换商品数据**
   ```
   - 替换 马桶贴模版.xlsx 为您的商品数据Excel文件
   - 将 HZ-M083 文件夹重命名为实际的商品货号
   - 替换 HZ-M083-1.png 等图片为实际的商品图片
   ```

4. **替换包装图片**
   ```
   - 圆柱体包装文件夹：放入圆柱体包装的图片
   - 长方体包装文件夹：放入长方体包装的图片
   ```

## 📋 目录结构

```
goods_template/
├── 马桶贴/                      ← 商品分类目录（需重命名为实际分类）
│   ├── 马桶贴模版.xlsx           ← 商品数据Excel文件
│   └── HZ-M083/                 ← 商品货号目录（需重命名为实际货号）
│       ├── 1.png                ← 商品图片（货号-序号.扩展名）
│       ├── 2.png                ← 支持jpg、png、jpeg、gif、bmp格式
│       ├── 3.png
│       ├── 4.png
│       └── 5.png
├── 圆柱体包装/                ← 固定包装目录（不可更改名称）
│   ├── 1.jpg                     ← 包装图片（序号.jpg）
│   ├── 2.png
│   └── ...
└── 长方体包装/                ← 固定包装目录（不可更改名称）
    ├── 1.jpg                     ← 包装图片（序号.jpg）
    ├── 2.png
    └── ...
```

## ⚠️ 重要说明

### 文件命名规则
- **商品图片**：使用 货号-1.png, 货号-2.png, 货号-3.png 等格式
- **包装图片**：使用 1.jpg, 2.png 等序号格式
- **图片格式**：支持 jpg、png、jpeg、gif、bmp 格式

### 目录命名规则
- **商品分类目录**：必须与Temu中的分类名称完全一致
- **商品货号目录**：以实际商品货号命名，如 HZ-M083、ABC-001 等
- **包装目录**：圆柱体包装、长方体包装是固定名称，不可更改

### 支持的图片格式
- jpg, jpeg, png, gif, bmp

## 🚀 快速开始

1. 复制 `goods_template` 到您的工作目录
2. 重命名商品分类文件夹
3. 替换Excel文件和图片
4. 在程序中选择该目录进行商品上传

## 📞 技术支持

如有问题，请参考程序中的使用说明或查看日志文件。
