#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu 自动化工具简化版主应用

提供简化的中文菜单界面，避免死循环问题。
适配Windows打包环境。
"""

import shutil
from pathlib import Path
from typing import Optional, Dict, List

import click
from loguru import logger

from biz.temu import Temu
from utils.user_manager import UserManager
from utils.logger_config import LoggerConfig
from utils.goods_processor import (
    load_fields_config,
    scan_subfolders,
    find_xlsx_files,
    process_excel_row_data
)


class TemuApp:
    """Temu 自动化工具简化版主应用类"""
    
    def __init__(self):
        """初始化应用"""
        # 初始化日志配置
        LoggerConfig.setup_logger()

        self.user_manager = UserManager()
        self.current_user_index: Optional[str] = None
        self.current_user_info: Optional[dict] = None
        self.current_temu: Optional[Temu] = None
        self.is_logged_in = False
        
    def run(self):
        """运行主应用"""
        self._show_welcome()
        
        try:
            while True:
                choice = self._show_main_menu()
                
                if choice == 0:
                    self._exit_app()
                    break
                elif choice == 1:
                    self._import_users()
                elif choice == 2:
                    self._login_user()
                elif choice == 3:
                    if self.is_logged_in:
                        self._upload_goods()
                    else:
                        click.echo(f"{click.style('❌ 请先登录用户', fg='red')}")
                elif choice == 4:
                    if self.is_logged_in:
                        self._logout_user()
                    else:
                        self._toggle_debug_mode()
                elif choice == 5:
                    if self.is_logged_in:
                        self._toggle_debug_mode()
                    else:
                        click.echo(f"{click.style('❌ 无效选项', fg='red')}")
                        
        except (KeyboardInterrupt, EOFError):
            click.echo(f"\n{click.style('👋 程序已退出', fg='yellow')}")
        except Exception as e:
            logger.exception("应用运行时发生异常")
            click.echo(f"{click.style('❌ 发生错误:', fg='red')} {str(e)}")
    
    def _show_welcome(self):
        """显示欢迎界面"""
        click.echo("\n" + "🌟" * 35)
        click.echo(f"{click.style('🎯 欢迎使用 Temu 自动化工具', fg='cyan', bold=True)}")
        click.echo("🌟" * 35)
        click.echo(f"{click.style('版本: v2.1.0', fg='green')} | {click.style('Windows版菜单界面', fg='yellow')}")
        click.echo("🌟" * 35)
    
    def _show_main_menu(self) -> int:
        """
        显示主菜单
        
        Returns:
            int: 用户选择的选项编号
        """
        try:
            # 显示当前状态
            self._show_current_status()
            
            # 显示菜单选项
            click.echo("\n" + "🔸" * 35)
            click.echo(f"{click.style('📋 主菜单', fg='cyan', bold=True)}")
            click.echo("🔸" * 35)
            
            click.echo(f"  [{click.style('1', fg='cyan', bold=True)}] "
                      f"{click.style('📥 导入用户', fg='white')}")
            
            click.echo(f"  [{click.style('2', fg='cyan', bold=True)}] "
                      f"{click.style('🔐 选择用户登录', fg='white')}")
            
            if self.is_logged_in:
                click.echo(f"  [{click.style('3', fg='cyan', bold=True)}] "
                          f"{click.style('📦 上传商品', fg='green')}")

                click.echo(f"  [{click.style('4', fg='cyan', bold=True)}] "
                          f"{click.style('🚪 登出', fg='yellow')}")

                click.echo(f"  [{click.style('5', fg='cyan', bold=True)}] "
                          f"{click.style('🔧 调试模式切换', fg='magenta')}")
            else:
                click.echo(f"  [{click.style('3', fg='black')}] "
                          f"{click.style('📦 上传商品', fg='black')} "
                          f"{click.style('(需要先登录)', fg='red')}")

                click.echo(f"  [{click.style('4', fg='cyan', bold=True)}] "
                          f"{click.style('🔧 调试模式切换', fg='magenta')}")

            click.echo(f"  [{click.style('0', fg='red', bold=True)}] "
                      f"{click.style('❌ 退出程序', fg='red')}")
            
            click.echo("🔸" * 35)
            
            # 简化的输入处理，避免死循环
            max_option = 5 if self.is_logged_in else 4
            
            choice = click.prompt(
                f"💡 请选择操作 (0-{max_option})",
                type=int,
                show_default=False
            )
            
            # 直接返回选择，在主循环中处理
            return choice
            
        except (click.Abort, EOFError, KeyboardInterrupt):
            return 0
        except (ValueError, TypeError):
            click.echo(f"{click.style('❌ 请输入有效的数字', fg='red')}")
            return -1  # 返回无效值，让主循环继续
    
    def _show_current_status(self):
        """显示当前状态"""
        click.echo(f"\n{click.style('📊 当前状态:', fg='blue', bold=True)}")
        
        # 显示用户数据状态
        if self.user_manager.load_users():
            user_count = self.user_manager.get_user_count()
            click.echo(f"  📋 用户数据: {click.style(f'{user_count} 个用户', fg='green')}")
        else:
            click.echo(f"  📋 用户数据: {click.style('未加载', fg='red')}")
        
        # 显示登录状态
        if self.is_logged_in and self.current_user_info:
            username = self.current_user_info.get('username', '未知用户')
            phone = self.current_user_info.get('phone', '')
            masked_phone = self.user_manager._mask_phone(phone)
            click.echo(f"  🔐 登录状态: {click.style('已登录', fg='green')} - "
                      f"{click.style(username, fg='white', bold=True)} "
                      f"({click.style(masked_phone, fg='yellow')})")
        else:
            click.echo(f"  🔐 登录状态: {click.style('未登录', fg='red')}")

        # 显示调试模式状态
        current_level = self._get_current_log_level()
        if current_level == "DEBUG":
            click.echo(f"  🔧 调试模式: {click.style('已启用', fg='yellow')}")
        else:
            click.echo(f"  🔧 调试模式: {click.style('已禁用', fg='green')}")
    
    def _import_users(self):
        """导入用户功能"""
        click.echo(f"\n{click.style('📥 导入用户数据', fg='cyan', bold=True)}")
        click.echo("=" * 50)
        
        try:
            # 获取用户输入的CSV文件路径
            csv_path = click.prompt(
                "请输入用户CSV文件的完整路径",
                type=str
            )
            
            csv_file = Path(csv_path)
            
            # 验证文件是否存在
            if not csv_file.exists():
                click.echo(f"{click.style('❌ 文件不存在:', fg='red')} {csv_path}")
                return
            
            # 验证文件扩展名
            if csv_file.suffix.lower() != '.csv':
                click.echo(f"{click.style('❌ 文件格式错误，请选择CSV文件', fg='red')}")
                return
            
            # 目标路径 - 使用与UserManager相同的路径解析方式
            target_path = self.user_manager.csv_path
            target_path.parent.mkdir(exist_ok=True)
            
            # 备份现有文件（如果存在）
            if target_path.exists():
                backup_path = target_path.with_suffix('.csv.backup')
                shutil.copy2(target_path, backup_path)
                click.echo(f"📋 已备份现有用户文件到: {backup_path}")
            
            # 复制新文件
            shutil.copy2(csv_file, target_path)
            click.echo(f"✅ 用户文件已导入到: {target_path}")
            
            # 重新加载用户数据
            if self.user_manager.load_users():
                user_count = self.user_manager.get_user_count()
                click.echo(f"🎉 成功加载 {click.style(str(user_count), fg='green', bold=True)} 个用户")
                
                # 显示用户列表预览
                if user_count > 0:
                    click.echo(f"\n{click.style('👥 用户列表预览:', fg='blue')}")
                    self.user_manager.display_users()
            else:
                click.echo(f"{click.style('❌ 用户文件格式错误，请检查文件内容', fg='red')}")
                
                # 恢复备份文件
                backup_path = target_path.with_suffix('.csv.backup')
                if backup_path.exists():
                    shutil.copy2(backup_path, target_path)
                    click.echo(f"🔄 已恢复原用户文件")
            
        except (click.Abort, EOFError, KeyboardInterrupt):
            click.echo(f"{click.style('❌ 用户取消导入', fg='yellow')}")
        except Exception as e:
            logger.exception("导入用户文件时发生异常")
            click.echo(f"{click.style('❌ 导入失败:', fg='red')} {str(e)}")
    
    def _login_user(self):
        """选择用户登录功能"""
        click.echo(f"\n{click.style('🔐 用户登录', fg='cyan', bold=True)}")
        click.echo("=" * 50)
        
        # 加载用户数据
        if not self.user_manager.load_users():
            click.echo(f"{click.style('❌ 无法加载用户数据，请先导入用户文件', fg='red')}")
            return
        
        if self.user_manager.get_user_count() == 0:
            click.echo(f"{click.style('❌ 没有可用的用户数据，请先导入用户文件', fg='red')}")
            return
        
        # 如果已经登录，询问是否切换用户
        if self.is_logged_in:
            current_username = self.current_user_info.get('username', '未知用户')
            if not click.confirm(f"当前已登录用户 {click.style(current_username, fg='white', bold=True)}，是否切换用户？"):
                return
        
        # 显示用户选择界面
        try:
            selected_index = self.user_manager.prompt_user_selection()
            
            if selected_index:
                # 执行登录
                self._perform_login(selected_index)
            else:
                click.echo(f"{click.style('❌ 未选择用户', fg='yellow')}")
        except (EOFError, KeyboardInterrupt):
            click.echo(f"{click.style('❌ 用户取消登录', fg='yellow')}")
    
    def _perform_login(self, user_index: str):
        """执行用户登录"""
        try:
            user_info = self.user_manager.get_user_by_index(user_index)
            if not user_info:
                click.echo(f"{click.style('❌ 用户信息不存在', fg='red')}")
                return
            
            username = user_info.get('username', '未知用户')
            click.echo(f"\n📱 正在登录用户: {click.style(username, fg='white', bold=True)}...")
            
            # 创建Temu实例
            temu = Temu.from_user_manager(user_index, self.user_manager)
            if not temu:
                click.echo(f"{click.style('❌ 创建登录实例失败', fg='red')}")
                return
            
            # 执行登录
            if temu.login():
                # 登录成功，保存状态
                self.current_user_index = user_index
                self.current_user_info = user_info
                self.current_temu = temu
                self.is_logged_in = True
                
                click.echo(f"🎉 {click.style('登录成功!', fg='green', bold=True)}")
                click.echo(f"   用户: {click.style(username, fg='white', bold=True)}")
                click.echo(f"   手机号: {click.style(self.user_manager._mask_phone(user_info.get('phone', '')), fg='yellow')}")
            else:
                click.echo(f"{click.style('❌ 登录失败，请检查用户名和密码', fg='red')}")
                
        except Exception as e:
            logger.exception("用户登录时发生异常")
            click.echo(f"{click.style('❌ 登录过程中发生错误:', fg='red')} {str(e)}")
    
    def _upload_goods(self):
        """上传商品功能"""
        click.echo(f"\n{click.style('📦 上传商品', fg='cyan', bold=True)}")
        click.echo("=" * 50)
        
        if not self.is_logged_in or not self.current_temu:
            click.echo(f"{click.style('❌ 请先登录用户', fg='red')}")
            return
        
        try:
            # 获取商品数据目录
            goods_path = click.prompt(
                "请输入商品数据文件夹路径",
                type=str
            )
            
            goods_dir = Path(goods_path)
            
            # 验证目录是否存在
            if not goods_dir.exists():
                click.echo(f"{click.style('❌ 目录不存在:', fg='red')} {goods_path}")
                return
            
            if not goods_dir.is_dir():
                click.echo(f"{click.style('❌ 路径不是目录:', fg='red')} {goods_path}")
                return
            
            # 显示当前登录用户信息
            username = self.current_user_info.get('username', '未知用户')
            click.echo(f"\n👤 当前登录用户: {click.style(username, fg='white', bold=True)}")
            click.echo(f"📁 商品数据目录: {click.style(str(goods_dir), fg='yellow')}")
            
            if click.confirm("确认开始上传商品吗？"):
                # 调用商品上传逻辑
                self._process_goods_upload(goods_dir)
                click.echo(f"\n🎉 {click.style('商品上传完成!', fg='green', bold=True)}")
            else:
                click.echo(f"{click.style('❌ 用户取消上传', fg='yellow')}")
                
        except (click.Abort, EOFError, KeyboardInterrupt):
            click.echo(f"{click.style('❌ 用户取消操作', fg='yellow')}")
        except Exception as e:
            logger.exception("上传商品时发生异常")
            click.echo(f"{click.style('❌ 上传过程中发生错误:', fg='red')} {str(e)}")
    
    def _process_goods_upload(self, goods_dir: Path):
        """处理商品上传逻辑"""
        try:
            # 扫描子文件夹
            subfolders = self._scan_subfolders(str(goods_dir))
            if not subfolders:
                click.echo(f"{click.style('❌ 目录下没有找到分类子文件夹', fg='red')}")
                return

            # 过滤 result/长方体包装/圆柱体包装 三个文件夹
            exclude_folders = {"result", "长方体包装", "圆柱体包装"}
            subfolders = [folder for folder in subfolders if folder not in exclude_folders]

            click.echo(f"📊 找到 {len(subfolders)} 个分类文件夹: {', '.join(subfolders)}")

            # 处理每个分类文件夹
            for subfolder in subfolders:
                category_folder_path = str(goods_dir / subfolder)
                click.echo(f"\n🔄 正在处理分类: {click.style(subfolder, fg='cyan', bold=True)}")
                self._process_category_folder(category_folder_path, subfolder)
                
        except Exception as e:
            logger.exception("商品上传处理时发生异常")
            click.echo(f"{click.style('❌ 商品上传处理失败:', fg='red')} {str(e)}")
    
    def _scan_subfolders(self, base_folder_path: str) -> list:
        """扫描基础文件夹下的所有子文件夹"""
        return scan_subfolders(base_folder_path)
    
    def _process_category_folder(self, category_folder_path: str, category_name: str):
        """处理单个分类文件夹下的所有xlsx文件"""
        try:
            click.echo(f"   📁 处理文件夹: {category_folder_path}")
            click.echo(f"   📝 分类名称: {category_name}")

            # 加载该分类的字段配置
            fulfilled_fields = load_fields_config(category_name)
            if not fulfilled_fields:
                click.echo(f"⚠️ 未找到分类 '{category_name}' 的配置")
                return

            # 查找该文件夹下的所有xlsx文件
            xlsx_files = find_xlsx_files(category_folder_path)
            if not xlsx_files:
                click.echo(f"⚠️ 文件夹 '{category_folder_path}' 下未找到xlsx文件")
                return

            # 创建Temu实例（使用当前已登录的实例）
            if not self.current_temu:
                click.echo(f"❌ 用户未登录")
                return


            click.echo(f"📊 找到 {len(xlsx_files)} 个Excel文件")

            # 处理每个Excel文件
            for xlsx_file in xlsx_files:
                click.echo(f"📄 正在处理文件: {Path(xlsx_file).name}")
                self._process_excel_file(xlsx_file, fulfilled_fields, category_folder_path, category_name)

        except Exception as e:
            logger.exception(f"处理分类文件夹 {category_name} 时发生异常")
            click.echo(f"{click.style('❌ 处理分类失败:', fg='red')} {category_name} - {str(e)}")



    def _process_excel_file(self, excel_file: str, fulfilled_fields: List[Dict],
                           goods_path: str, category: str):
        """处理单个Excel文件"""
        try:
            click.echo(f"📄 正在处理文件: {excel_file}")

            # 创建DataUtil实例（会自动初始化状态列）
            data_util = self._create_data_util(excel_file)
            datas = data_util.read()

            # 显示处理统计
            stats = data_util.get_processing_statistics()
            if "error" not in stats:
                click.echo(f"📊 处理统计: 总计{stats['total_rows']}行, "
                          f"成功{stats['success_count']}行, "
                          f"失败{stats['failed_count']}行, "
                          f"待处理{stats['pending_count']}行")

            # 处理每一行Excel数据
            for row_index, data in enumerate(datas):
                actual_row = row_index + 1  # Excel行号从1开始
                logger.info(f"正在处理第 {actual_row} 行数据...")

                # 关键步骤：选择商品分类
                click.echo(f"📱 正在选择分类: {category}...")
                result = self.current_temu.select_category(category)
                if not result.success:
                    click.echo(f"❌ 选择分类 '{category}' 失败: {result.message}")
                    continue

                click.echo(f"✅ 选择分类 '{category}' 成功")

                # 检查是否已经成功处理过
                if data_util.is_already_processed(data):
                    sku_no = data.get("货号", "未知")
                    click.echo(f"⏭️ [{sku_no}] 第 {actual_row} 行已成功处理，跳过")
                    continue

                try:
                    data_json = process_excel_row_data(data, fulfilled_fields, goods_path)
                    logger.debug(f"处理数据: {data_json}")

                    # 创建商品
                    success, message = self._create_goods(category, data_json)

                    # 更新Excel状态
                    if success:
                        data_util.update_status(row_index, "成功", message)
                    else:
                        data_util.update_status(row_index, "失败", message)
                        click.echo(f"❌ 处理中断：第 {actual_row} 行数据处理失败")
                        click.echo(f"   文件: {excel_file}")
                        click.echo(f"   分类: {category}")
                        click.echo(f"   失败原因: {message}")
                        continue

                except Exception as e:
                    error_message = f"处理异常: {str(e)}"
                    logger.exception(f"处理第 {actual_row} 行数据时发生异常")
                    data_util.update_status(row_index, "失败", error_message)
                    click.echo(f"❌ 处理中断：第 {actual_row} 行数据处理异常")
                    click.echo(f"   文件: {excel_file}")
                    click.echo(f"   分类: {category}")
                    click.echo(f"   异常信息: {error_message}")
                    continue  # 中断当前文件的处理

        except Exception as e:
            logger.exception(f"处理Excel文件 {excel_file} 时发生异常")
            click.echo(f"❌ 处理Excel文件失败: {str(e)}")

    def _create_data_util(self, excel_file: str):
        """创建DataUtil实例"""
        # 导入DataUtil类
        from index import DataUtil
        return DataUtil(excel_file)

    def _create_goods(self, category_name: str, goods_data: Dict) -> tuple[bool, str]:
        """创建商品

        Args:
            category_name: 分类名称
            goods_data: 商品数据

        Returns:
            tuple[bool, str]: (创建成功状态, 状态消息)
        """
        response = self.current_temu.create_goods(goods_data)

        sku_no = goods_data.get("货号").get("value")
        if not response.success:
            error_message = f"创建商品失败: {response.message}"
            if response.data and "missing_field" in response.data:
                error_message += f" - 缺失字段: {response.data['missing_field']}"

            click.echo(f"❌ [{sku_no}] {error_message}")
            return False, error_message

        success_message = "商品创建成功"
        click.echo(f"✅ [{sku_no}] {success_message}")
        return True, success_message


    
    def _logout_user(self):
        """登出用户功能"""
        if not self.is_logged_in:
            click.echo(f"{click.style('❌ 当前没有用户登录', fg='red')}")
            return
        
        username = self.current_user_info.get('username', '未知用户')
        
        if click.confirm(f"确认登出用户 {click.style(username, fg='white', bold=True)} 吗？"):
            # 清除登录状态
            self.current_user_index = None
            self.current_user_info = None
            self.current_temu = None
            self.is_logged_in = False
            
            click.echo(f"👋 {click.style('已成功登出', fg='green')}")
        else:
            click.echo(f"{click.style('❌ 取消登出', fg='yellow')}")
    
    def _exit_app(self):
        """退出应用"""
        try:
            if self.is_logged_in:
                username = self.current_user_info.get('username', '未知用户')
                click.echo(f"\n👋 当前登录用户: {click.style(username, fg='white', bold=True)}")
                
                if click.confirm("是否要登出后退出？", default=True):
                    self._logout_user()
        except (click.Abort, EOFError, KeyboardInterrupt):
            # 用户取消或中断，直接退出
            pass
        
        click.echo(f"\n{click.style('👋 感谢使用 Temu 自动化工具！', fg='cyan', bold=True)}")
        click.echo("🌟" * 35)

    def _toggle_debug_mode(self):
        """切换调试模式"""
        click.echo(f"\n{click.style('🔧 调试模式设置', fg='magenta', bold=True)}")
        click.echo("=" * 50)

        current_level = self._get_current_log_level()

        if current_level == "DEBUG":
            click.echo(f"当前状态: {click.style('DEBUG模式已启用', fg='yellow')}")
            if click.confirm("是否要禁用DEBUG模式？", default=False):
                LoggerConfig.disable_debug()
                click.echo(f"{click.style('✅ DEBUG模式已禁用', fg='green')}")
                click.echo("现在只显示INFO级别及以上的日志")
        else:
            click.echo(f"当前状态: {click.style('DEBUG模式已禁用', fg='green')}")
            if click.confirm("是否要启用DEBUG模式？", default=False):
                LoggerConfig.enable_debug()
                click.echo(f"{click.style('✅ DEBUG模式已启用', fg='yellow')}")
                click.echo("现在会显示详细的调试信息")

    def _get_current_log_level(self) -> str:
        """获取当前日志级别"""
        # 通过检查logger的handlers来确定当前级别
        for handler in logger._core.handlers.values():
            if hasattr(handler, '_levelno'):
                if handler._levelno <= 10:  # DEBUG级别
                    return "DEBUG"
        return "INFO"


def main():
    """主函数"""
    app = TemuApp()
    app.run()


if __name__ == "__main__":
    main()
