🖥️ Temu自动化工具 V1.0.0 - 系统要求

📋 最低系统要求
==================

操作系统：
- Windows 10 (64位) 或更高版本
- Windows 11 (推荐)

硬件要求：
- 内存：4GB RAM (推荐 8GB)
- 存储：500MB 可用磁盘空间
- 处理器：Intel/AMD 双核处理器或更高

必需软件：
- Chrome浏览器 (最新版本)
  下载地址：https://www.google.com/intl/zh-CN/chrome/
- 稳定的互联网连接

开发环境软件（源码运行需要）：
- Python 3.12.10 (推荐版本)
  下载页面：https://www.python.org/downloads/windows/
  直接下载：https://www.python.org/ftp/python/3.12.10/python-3.12.10-amd64.exe
  注意：使用exe版本无需安装Python

🔧 可选组件
==================

通常系统已预装，如遇问题可手动安装：

Visual C++ 运行库：
- Microsoft Visual C++ 2015-2022 Redistributable (x64)
- 下载地址：https://aka.ms/vs/17/release/vc_redist.x64.exe

.NET Framework：
- .NET Framework 4.7.2 或更高版本
- Windows 10/11 通常已预装

⚠️ 重要提醒
==================

1. 🌐 Chrome浏览器是必需的
   - 程序依赖Chrome进行网页自动化操作
   - 必须安装最新版本以确保兼容性
   - 如果Chrome版本过旧可能导致程序无法正常工作

2. 🐍 Python安装说明
   - 如果只使用TemuAutoTool.exe，无需安装Python
   - 如果需要运行源代码，请安装Python 3.12.10
   - 安装时建议勾选"Add Python to PATH"选项

3. ⏱️ 首次运行说明
   - 首次运行可能需要5-10秒加载时间
   - 这是正常现象，程序需要解压到临时目录
   - 后续启动会更快

🔍 兼容性说明
==================

完全兼容：
✅ Windows 11 (所有版本)
✅ Windows 10 (版本1903及以上)

基本兼容：
⚠️ Windows 8.1 (需要更新系统组件)
⚠️ Windows 7 SP1 (需要安装额外运行库)

不支持：
❌ Windows XP/Vista
❌ 32位操作系统

📞 安装帮助
==================

如遇到安装问题，请按以下步骤操作：

1. 确认系统版本符合要求
2. 下载并安装Chrome浏览器
3. 如提示缺少运行库，请安装Visual C++运行库
4. 确保有稳定的网络连接
5. 临时关闭防病毒软件进行测试

技术支持：
- 查看程序目录下的logs文件夹中的日志文件
- 参考docs文件夹中的详细文档
- 确认所有必需软件已正确安装

版本：V1.0.0
更新日期：2025年7月
