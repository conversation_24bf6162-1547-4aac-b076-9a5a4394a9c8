🖥️ Temu自动化工具 - 系统要求

📋 最低系统要求
==================

操作系统：
- Windows 10 (64位) 或更高版本
- Windows 11 (推荐)

硬件要求：
- 内存：4GB RAM (推荐 8GB)
- 存储：500MB 可用磁盘空间
- 处理器：Intel/AMD 双核处理器或更高

必需软件：
- Chrome浏览器 (最新版本)
- 稳定的互联网连接

🔧 可选组件
==================

通常系统已预装，如遇问题可手动安装：

Visual C++ 运行库：
- Microsoft Visual C++ 2015-2022 Redistributable (x64)
- 下载地址：https://aka.ms/vs/17/release/vc_redist.x64.exe

.NET Framework：
- .NET Framework 4.7.2 或更高版本
- Windows 10/11 通常已预装

⚠️ 注意事项
==================

1. 首次运行可能需要5-10秒加载时间
2. 确保Chrome浏览器为默认浏览器或已正确安装
3. 建议关闭不必要的程序以确保最佳性能
4. 如遇到防病毒软件误报，请添加到白名单

🔍 兼容性说明
==================

完全兼容：
✅ Windows 11 (所有版本)
✅ Windows 10 (版本1903及以上)

基本兼容：
⚠️ Windows 8.1 (需要更新系统组件)
⚠️ Windows 7 SP1 (需要安装额外运行库)

不支持：
❌ Windows XP/Vista
❌ 32位操作系统

📞 技术支持
==================

如遇到系统兼容性问题，请：
1. 确认系统版本符合要求
2. 更新Chrome浏览器到最新版本
3. 安装必需的系统组件
4. 查看程序目录下的日志文件
5. 参考部署指南中的故障排除部分

版本：v2.1.0
更新日期：2025年7月
