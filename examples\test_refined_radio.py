#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的Radio组件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from biz.temu import Temu
from components.radio import RadioComponent

def test_refined_radio():
    """测试优化后的Radio组件"""
    
    print("🎯 测试优化后的Radio组件")
    print("=" * 50)
    
    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False
        
        page = temu.page
        print("✅ 登录成功")
        
        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")
        
        # 3. 创建Radio组件实例
        print("\n🔧 创建RadioComponent实例...")
        radio_component = RadioComponent(tab)
        
        # 4. 测试不同的radio选择
        test_cases = [
            ("外包装类型", {"value": "软包装+硬物", "require": 1}),
            ("外包装形状", {"value": "其他不规则形状", "require": 1}),
        ]
        
        for field_title, data in test_cases:
            print(f"\n🎯 测试: {field_title} = {data['value']}")
            
            result = radio_component.fill_data(field_title, data)
            
            if result.success:
                print(f"✅ {field_title} 成功: {result.message}")
            else:
                print(f"❌ {field_title} 失败: {result.message}")
            
            tab.wait(2)
        
        print(f"\n🎉 Radio组件测试完成！")
        print("页面将保持打开状态供查看...")
        print("按 Ctrl+C 退出程序")
        
        try:
            while True:
                tab.wait(10)
                print("⏳ 页面仍然打开中...")
        except KeyboardInterrupt:
            print("\n👋 程序退出")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False

if __name__ == "__main__":
    test_refined_radio()
