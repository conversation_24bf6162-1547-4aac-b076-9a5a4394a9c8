from loguru import logger

from utils.hhcsv import HHCSV


class DataUtil:
    DEFAULT_HEADERS = ["index"]

    def __init__(self, csv_path: str, headers: list[str] = None):
        if not csv_path:
            raise ValueError("csv 路径不能为空")
        self.headers = headers if headers is not None else self.DEFAULT_HEADERS.copy()
        self._init_csv(csv_path, headers)

    def _init_csv(self, csv_path: str, headers: list[str]):
        try:
            # 设置CSV文件路径，使用os.path.join确保跨平台兼容
            self._csv = HHCSV(csv_path, headers)
        except Exception as e:
            logger.error(f"初始化CSV文件失败: {str(e)}")
            raise

    def list(self):
        try:
            return self._csv.data
        except Exception as e:
            logger.error(f"查询全部数据失败: {str(e)}")
            return []

    def get(self, index):
        try:
            result = self._csv.query({"index": str(index)})
            return result[0] if result else {}
        except Exception as e:
            logger.error(f"查询 {index} 数据失败: {str(e)}")
            return {}

    def add(self, data):
        try:
            self._csv.add_row(data)
        except Exception as e:
            logger.error(f"新增数据失败, data={data}, error={str(e)}")

    def update(self, index, data):
        try:
            criteria = {"index": index}
            self._csv.update_row(criteria, data)
        except Exception as e:
            logger.error(f"更新数据失败, index={index}, data={data}, error={str(e)}")

    def flush(self):
        try:
            self._csv.load()
        except Exception as e:
            logger.error(f"刷新数据失败, error={str(e)}")
