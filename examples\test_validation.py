"""
测试字段验证功能

验证 validate_required_field 方法能正确处理不同类型的值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.base_component import BaseComponent


def test_validation():
    """测试字段验证功能"""
    
    print("🎯 测试字段验证功能")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        # 字符串类型
        {"title": "字符串-有效值", "data": {"require": 1, "value": "test"}, "expected": None},
        {"title": "字符串-空值", "data": {"require": 1, "value": ""}, "expected": "error"},
        {"title": "字符串-空白", "data": {"require": 1, "value": "   "}, "expected": "error"},
        {"title": "字符串-非必填", "data": {"require": 0, "value": ""}, "expected": None},
        
        # 整数类型
        {"title": "整数-有效值", "data": {"require": 1, "value": 123}, "expected": None},
        {"title": "整数-零值", "data": {"require": 1, "value": 0}, "expected": None},
        {"title": "整数-负值", "data": {"require": 1, "value": -5}, "expected": None},
        
        # 浮点数类型
        {"title": "浮点数-有效值", "data": {"require": 1, "value": 25.99}, "expected": None},
        {"title": "浮点数-零值", "data": {"require": 1, "value": 0.0}, "expected": None},
        
        # None类型
        {"title": "None值-必填", "data": {"require": 1, "value": None}, "expected": "error"},
        {"title": "None值-非必填", "data": {"require": 0, "value": None}, "expected": None},
        
        # 布尔类型
        {"title": "布尔值-True", "data": {"require": 1, "value": True}, "expected": None},
        {"title": "布尔值-False", "data": {"require": 1, "value": False}, "expected": None},
        
        # 列表类型
        {"title": "列表-非空", "data": {"require": 1, "value": [1, 2, 3]}, "expected": None},
        {"title": "列表-空", "data": {"require": 1, "value": []}, "expected": None},
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for test_case in test_cases:
        title = test_case["title"]
        data = test_case["data"]
        expected = test_case["expected"]
        
        print(f"\n🧪 测试: {title}")
        print(f"   数据: {data}")
        
        try:
            result = BaseComponent.validate_required_field(title, data)
            
            if expected == "error":
                if result is not None and hasattr(result, 'success') and not result.success:
                    print(f"   ✅ 预期错误，实际错误: {result.message}")
                    success_count += 1
                else:
                    print(f"   ❌ 预期错误，但验证通过了")
            else:  # expected is None
                if result is None:
                    print(f"   ✅ 预期通过，实际通过")
                    success_count += 1
                else:
                    print(f"   ❌ 预期通过，但返回错误: {result.message}")
                    
        except Exception as e:
            print(f"   ❌ 验证过程中发生异常: {str(e)}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    print("🚀 开始测试字段验证功能...")
    test_validation()
    print("\n🎉 测试完成！")
