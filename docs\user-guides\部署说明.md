# Temu 自动化工具 - 部署说明

## 🎯 概述

本文档说明如何将 Temu 自动化工具部署给最终用户使用。新版本采用了美观的中文选择框界面，用户体验更加友好。

## 📦 部署准备

### 1. 环境要求

- Python 3.8 或更高版本
- Chrome 或 Chromium 浏览器
- 稳定的网络连接

### 2. 必要文件

确保以下文件存在于部署包中：

```
dp_temu/
├── biz/                    # 业务逻辑模块
├── utils/                  # 工具模块
├── components/             # UI组件
├── config/                 # 配置文件
├── data/                   # 数据文件
│   ├── users.csv          # 用户数据文件（需要用户配置）
│   └── users.csv.copy     # 用户数据模板
├── index.py               # 主程序入口
├── demo_user_selection.py # 演示工具
├── requirements.txt       # 依赖包列表
├── .env                   # 环境配置（需要用户配置）
├── 用户管理使用说明.md    # 用户说明文档
└── 部署说明.md           # 本文档
```

## 🚀 部署步骤

### 1. 安装依赖

```bash
# 创建虚拟环境（推荐）
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

# 安装依赖包
pip install -r requirements.txt
```

### 2. 配置用户数据

复制用户数据模板：
```bash
cp data/users.csv.copy data/users.csv
```

编辑 `data/users.csv` 文件，添加用户信息：
```csv
index,phone,password,username
1,13800138000,your_password,用户1
2,13800138001,your_password,用户2
```

### 3. 配置环境变量

复制环境配置模板：
```bash
cp .env.example .env  # 如果存在模板文件
```

编辑 `.env` 文件，配置必要的环境变量。

### 4. 测试安装

运行演示工具测试功能：
```bash
# 测试用户选择功能
python demo_user_selection.py chinese

# 查看用户列表
python index.py users

# 测试登录功能
python index.py login
```

## 👥 用户使用指南

### 基本操作流程

1. **查看可用账号**
   ```bash
   python index.py users
   ```

2. **登录测试**
   ```bash
   python index.py login
   ```
   系统会显示美观的中文选择框，用户可以：
   - 输入数字选择账号
   - 确认选择
   - 或输入 0 退出

3. **创建商品**
   ```bash
   python index.py cg
   ```
   系统会先让用户选择账号，然后输入商品数据路径。

### 界面说明

新版本采用了美观的中文选择框界面：

```
🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹
🎯  Temu 账号选择器
🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹
📊 共找到 2 个可用账号

  [1] 用户1 (138****8000) - ✅ 可用
  [2] 用户2 (138****8001) - ✅ 可用

  [0] 退出程序
🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹🔹
💡 请输入选项编号 (1-2, 0=退出):
```

### 特色功能

- 🎨 **彩色界面**：不同状态用不同颜色显示
- 🔐 **隐私保护**：自动隐藏手机号中间4位
- ✅ **状态验证**：显示账号是否可用
- 🔍 **确认机制**：选择后需要确认，避免误操作
- 🛡️ **错误处理**：友好的错误提示

## 🔧 故障排除

### 常见问题

1. **用户数据加载失败**
   - 检查 `data/users.csv` 文件是否存在
   - 确认文件格式正确
   - 检查文件编码为 UTF-8

2. **没有可用用户**
   - 确认 `data/users.csv` 中有有效数据
   - 检查所有必填字段都已填写

3. **登录失败**
   - 验证手机号和密码是否正确
   - 检查网络连接
   - 确认浏览器正常工作

4. **界面显示异常**
   - 确认终端支持彩色显示
   - 尝试使用不同的终端程序

### 调试模式

如果遇到问题，可以查看详细日志：
```bash
# 查看日志输出
python index.py login --verbose  # 如果支持详细模式
```

## 📋 维护说明

### 定期维护

1. **更新用户数据**
   - 定期检查 `data/users.csv` 文件
   - 更新过期的密码
   - 添加或删除用户账号

2. **备份重要数据**
   - 备份用户配置文件
   - 备份处理结果文件

3. **更新依赖包**
   ```bash
   pip install --upgrade -r requirements.txt
   ```

### 安全建议

1. **保护敏感信息**
   - 不要将 `data/users.csv` 提交到版本控制
   - 定期更换密码
   - 限制文件访问权限

2. **网络安全**
   - 使用安全的网络环境
   - 避免在公共网络上运行

## 📞 技术支持

如果遇到技术问题，请提供以下信息：

1. 错误信息截图
2. 使用的操作系统和Python版本
3. 执行的具体命令
4. `data/users.csv` 文件格式（隐藏敏感信息）

## 🎉 新功能亮点

### v2.1.0 更新内容

- ✨ **全新中文选择框界面**：美观、直观、易用
- 🌈 **彩色显示支持**：不同状态用不同颜色区分
- 🔍 **确认选择机制**：避免误操作，提升安全性
- 📱 **移动端友好**：适配不同终端环境
- 🛠️ **演示工具**：独立的功能演示和测试工具

用户现在可以享受更加友好和专业的操作体验！
