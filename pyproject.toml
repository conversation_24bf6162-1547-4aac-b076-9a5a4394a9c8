[project]
name = "temu_cli"
version = "0.1.0"
description = "Temu CLI"
requires-python = ">=3.7"
dependencies = [
    "loguru",
    "click",
    "python-dotenv",
    "DrissionPage",
    "pyautogui",
    "pyperclip",
    "Faker",
    "fake_useragent",
    "pypinyin",
    "curl-cffi",
    "openpyxl",  # 轻量级 Excel 文件处理，替代 pandas
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]  # 在当前目录下查找包
include = ["src*", "config*", "data*"]
exclude = ["tests*", "examples*"]

[tool.setuptools.package-data]
"src" = ["*.py"]  # 确保包含所有 Python 文件

[tool.pytest.ini_options]
pythonpath = [
    "."
]

[tool.black]
line-length = 120
target-version = ["py38", "py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
    # directories
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | __pycache__
)/
'''

[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.setuptools]
py-modules = []
