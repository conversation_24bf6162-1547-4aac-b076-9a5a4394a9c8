"""
商品数据处理工具模块

提供商品数据处理的通用函数，被index.py和app.py共同使用
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import click
from loguru import logger

from utils.common import get_project_root_path
from config.image_config import ImageConfig


def load_fields_config(category: str) -> List[Dict]:
    """
    根据分类名称加载字段配置
    
    Args:
        category: 分类名称
        
    Returns:
        List[Dict]: 字段配置列表
    """
    try:
        config_path = Path(get_project_root_path()) / "config" / "fileds.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            fields = json.load(f)
        return fields.get(category, [])
    except Exception as e:
        logger.error(f"加载字段配置失败: {str(e)}")
        return []


def scan_subfolders(base_path: str) -> List[str]:
    """
    扫描基础路径下的所有子文件夹
    
    Args:
        base_path: 基础路径
        
    Returns:
        List[str]: 子文件夹名称列表
    """
    try:
        subfolders = []
        base_path_obj = Path(base_path)
        if base_path_obj.exists():
            for item in base_path_obj.iterdir():
                if item.is_dir():
                    subfolders.append(item.name)
        return sorted(subfolders)
    except Exception as e:
        logger.error(f"扫描子文件夹失败: {str(e)}")
        return []


def find_xlsx_files(folder_path: str) -> List[str]:
    """
    查找文件夹下的所有xlsx文件
    
    Args:
        folder_path: 文件夹路径
        
    Returns:
        List[str]: xlsx文件路径列表
    """
    try:
        xlsx_files = []
        folder_path_obj = Path(folder_path)
        if folder_path_obj.exists():
            for file in folder_path_obj.iterdir():
                if file.suffix.lower() == '.xlsx' and not file.name.startswith('~'):
                    xlsx_files.append(str(file))
        return xlsx_files
    except Exception as e:
        logger.error(f"查找Excel文件失败: {str(e)}")
        return []


def build_field_data(field: Dict, data: Dict) -> Dict:
    """
    构建单个字段的数据
    
    Args:
        field: 字段配置
        data: Excel行数据
        
    Returns:
        Dict: 构建的字段数据
    """
    try:
        field_data = {"require": field.get("require", 0)}

        # 如果存在type，添加到field_data
        if field_type := field.get("type"):
            field_data["type"] = field_type

        # 获取字段值：优先使用Excel数据，其次使用默认值
        if excel_column := field.get("excel_column"):
            if excel_value := data.get(excel_column):
                field_data["value"] = excel_value
            elif default_value := field.get("value"):
                field_data["value"] = default_value
        elif default_value := field.get("value"):
            field_data["value"] = default_value

        return field_data
    except Exception as e:
        logger.error(f"构建字段数据失败: {str(e)}")
        return {"require": 0, "value": ""}


def validate_and_rename_images(goods_path: Path, sku_no: str) -> bool:
    """
    验证和重命名商品图片
    
    Args:
        goods_path: 商品图片路径
        sku_no: 商品SKU编号
        
    Returns:
        bool: 是否成功
    """
    try:
        if not goods_path.exists():
            logger.error(f"商品图片路径不存在: {goods_path}")
            return False
            
        # 检查是否有足够的图片
        image_files = []
        supported_formats = ImageConfig.get_required_formats()  # 支持的图片格式

        for i in range(1, 6):  # 需要5张图片
            image_found = False

            # 尝试不同的图片格式
            for ext in supported_formats:
                image_name = f"{sku_no}-{i}{ext}"
                image_path = goods_path / image_name
                if image_path.exists():
                    image_files.append(image_path)
                    image_found = True
                    logger.debug(f"找到图片: {image_name}")
                    break

            if not image_found:
                # 列出所有尝试的格式
                attempted_names = [f"{sku_no}-{i}{ext}" for ext in supported_formats]
                logger.error(f"缺少商品图片 {i}，尝试了以下格式: {', '.join(attempted_names)}")
                return False
            
        logger.info(f"商品 {sku_no} 的图片验证通过")
        return True
    except Exception as e:
        logger.error(f"验证和重命名图片失败: {str(e)}")
        return False


def process_special_fields(data_json: Dict, data: Dict, goods_folder_path: str) -> None:
    """
    处理特殊字段：商品轮播图和外包装图片
    
    Args:
        data_json: 处理后的数据JSON
        data: 原始Excel行数据
        goods_folder_path: 商品文件夹路径
    """
    try:
        goods_path = Path(goods_folder_path) / str(data.get('货号'))
        
        # 商品轮播图的 value 放 货号对应的文件夹路径
        if "商品轮播图" in data_json:
            # 验证和重命名图片文件
            validate_and_rename_images(goods_path, str(data.get('货号')))
            data_json["商品轮播图"]["value"] = f"{goods_path}/"

        # 外包装形状: 圆柱体 长方体
        if "外包装图片" in data_json:
            # 外包装图片路径应该在goods_path的上一级目录中
            parent_path = goods_path.parent.parent
            package_path = None
            if data.get("外包装形状") == "圆柱体":
                package_path = f"{parent_path}/圆柱体包装/"
            elif data.get("外包装形状") == "长方体":
                package_path = f"{parent_path}/长方体包装/"

            _path = Path(package_path)
            if not _path.exists():
                raise Exception(f"{package_path}路径不存在...")

            data_json["外包装图片"]["value"] = package_path

        # 处理商品规格 - 整合所有product_skc_字段为统一的规格数据结构
        specifications = []
        fields_to_remove = []

        # 收集所有product_skc_相关的字段配对
        for i in range(1, 6):
            parent_field = f"父规格{i}"
            spec_field = f"商品规格{i}"

            # 检查这些字段是否在data_json中存在
            if parent_field in data_json and spec_field in data_json:
                # 数据已经被预处理过，直接从value中获取
                parent_data = data_json[parent_field]
                spec_data = data_json[spec_field]

                # 获取实际的属性名和属性值
                if isinstance(parent_data, dict) and isinstance(spec_data, dict):
                    attr_name = parent_data.get("value", "")
                    attr_value = spec_data.get("value", "")

                    # 只有当属性名和属性值都不为空时才添加
                    if attr_name and str(attr_name).strip() and attr_value and str(attr_value).strip():
                        specifications.append({
                            "name": str(attr_name).strip(),
                            "values": attr_value
                        })
                    # 标记这些字段需要移除
                    fields_to_remove.extend([parent_field, spec_field])

        # 处理预览图, 采用货号 文件夹下的第一张图片
        if "预览图" in data_json:
            goods_path = Path(goods_folder_path) / str(data.get('货号'))
            sku_no = str(data.get('货号'))
            supported_formats = ImageConfig.get_required_formats()  # 支持的图片格式

            preview_path = None
            # 尝试不同的图片格式
            for ext in supported_formats:
                candidate_path = goods_path / f"{sku_no}-1{ext}"
                if candidate_path.exists():
                    preview_path = candidate_path
                    break

            if not preview_path:
                attempted_names = [f"{sku_no}-1{ext}" for ext in supported_formats]
                raise Exception(f"预览图不存在，尝试了以下格式: {', '.join(attempted_names)}")

            data_json["预览图"]["value"] = str(preview_path.absolute())

        # 处理详情图文
        if "详情图文" in data_json:
            goods_path = Path(goods_folder_path) / str(data.get('货号'))
            if not goods_path.exists():
                raise Exception(f"{goods_path} 图片路径不存在")

            data_json["详情图文"]["value"] = f"{goods_path}/"
    except Exception as e:
        logger.error(f"处理特殊字段失败: {str(e)}")
        raise


def process_excel_row_data(data: Dict, fulfilled_fields: List[Dict], goods_folder_path: str) -> Dict:
    """
    处理单行Excel数据，生成商品数据JSON
    
    Args:
        data: Excel行数据
        fulfilled_fields: 字段配置列表
        goods_folder_path: 商品文件夹路径
        
    Returns:
        Dict: 处理后的数据JSON
    """
    try:
        data_json = {}

        # 处理每个字段
        for field in fulfilled_fields:
            field_title = field.get("title")
            field_data = build_field_data(field, data)
            data_json[field_title] = field_data

        # 处理特殊字段
        process_special_fields(data_json, data, goods_folder_path)

        return data_json
    except Exception as e:
        logger.error(f"处理Excel行数据失败: {str(e)}")
        raise
