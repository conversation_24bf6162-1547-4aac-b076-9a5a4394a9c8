#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片配置管理

统一管理项目中所有图片相关的配置，包括支持的格式、大小限制等
"""

from typing import Set, List


class ImageConfig:
    """图片配置类"""
    
    # 支持的图片格式（推荐格式）
    SUPPORTED_FORMATS: Set[str] = {'.jpg', '.jpeg', '.png'}
    
    # 扩展支持的图片格式（包含更多格式）
    EXTENDED_FORMATS: Set[str] = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
    
    # 商品图片必需的格式（用于商品上传验证）
    REQUIRED_FORMATS: List[str] = ['.png', '.jpg', '.jpeg']
    
    # 图片大小限制（字节）
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 图片数量限制
    MAX_CAROUSEL_IMAGES: int = 5  # 轮播图最大数量
    MIN_CAROUSEL_IMAGES: int = 5  # 轮播图最小数量（必须5张）
    
    @classmethod
    def get_supported_formats(cls, extended: bool = False) -> Set[str]:
        """
        获取支持的图片格式
        
        Args:
            extended: 是否返回扩展格式列表
            
        Returns:
            Set[str]: 支持的图片格式集合
        """
        return cls.EXTENDED_FORMATS if extended else cls.SUPPORTED_FORMATS
    
    @classmethod
    def get_required_formats(cls) -> List[str]:
        """
        获取商品图片必需的格式列表（按优先级排序）
        
        Returns:
            List[str]: 必需格式列表
        """
        return cls.REQUIRED_FORMATS.copy()
    
    @classmethod
    def is_supported_format(cls, file_extension: str, extended: bool = False) -> bool:
        """
        检查文件扩展名是否被支持
        
        Args:
            file_extension: 文件扩展名（如 '.jpg'）
            extended: 是否使用扩展格式列表
            
        Returns:
            bool: 是否支持该格式
        """
        formats = cls.get_supported_formats(extended)
        return file_extension.lower() in formats
    
    @classmethod
    def is_valid_file_size(cls, file_size: int) -> bool:
        """
        检查文件大小是否有效
        
        Args:
            file_size: 文件大小（字节）
            
        Returns:
            bool: 文件大小是否有效
        """
        return 0 < file_size <= cls.MAX_FILE_SIZE
    
    @classmethod
    def format_file_size(cls, file_size: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            file_size: 文件大小（字节）
            
        Returns:
            str: 格式化的文件大小字符串
        """
        if file_size < 1024:
            return f"{file_size}B"
        elif file_size < 1024 * 1024:
            return f"{file_size / 1024:.1f}KB"
        else:
            return f"{file_size / (1024 * 1024):.1f}MB"
    
    @classmethod
    def get_format_description(cls) -> str:
        """
        获取支持格式的描述文本
        
        Returns:
            str: 格式描述
        """
        supported = ', '.join(sorted(cls.SUPPORTED_FORMATS))
        extended = ', '.join(sorted(cls.EXTENDED_FORMATS - cls.SUPPORTED_FORMATS))
        
        description = f"推荐格式: {supported}"
        if extended:
            description += f"\n扩展格式: {extended}"
        
        return description


# 便捷的全局常量
SUPPORTED_IMAGE_FORMATS = ImageConfig.SUPPORTED_FORMATS
EXTENDED_IMAGE_FORMATS = ImageConfig.EXTENDED_FORMATS
REQUIRED_IMAGE_FORMATS = ImageConfig.REQUIRED_FORMATS
MAX_IMAGE_SIZE = ImageConfig.MAX_FILE_SIZE
