#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试父规格字段查找功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DrissionPage import ChromiumPage
from components.product_specification import ProductSpecificationComponent
from biz.user import login
import time

def test_field_finding():
    """测试父规格字段查找"""
    try:
        print("🎯 测试父规格字段查找")
        print("=" * 50)
        
        # 1. 初始化浏览器
        tab = ChromiumPage()
        
        # 2. 登录
        print("📱 正在登录...")
        login_result = login(tab)
        if not login_result.success:
            print(f"❌ 登录失败: {login_result.message}")
            return
        print("✅ 登录成功")
        
        # 3. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        tab.get("https://seller.kuajingmaihuo.com/goods/create")
        time.sleep(5)
        print("✅ 页面加载完成")
        
        # 4. 创建ProductSpecificationComponent实例
        print("\n🔧 创建ProductSpecificationComponent实例...")
        spec_component = ProductSpecificationComponent(tab)
        
        # 5. 测试字段查找
        print(f"\n🔍 开始查找父规格字段...")
        container = spec_component.find_field_container("商品规格")
        
        if container:
            print(f"✅ 找到父规格容器!")
            print(f"   容器标签: {container.tag}")
            print(f"   容器类名: {container.attr('class')}")
            
            # 查找容器内的文本
            text_content = container.text
            print(f"   容器文本: {text_content[:100]}...")
            
        else:
            print(f"❌ 未找到父规格容器")
            
            # 尝试查找页面上所有包含"父规格"的元素
            print("\n🔍 查找页面上所有包含'父规格'的元素...")
            elements = tab.eles("x://*[contains(text(), '父规格')]")
            if elements:
                print(f"   找到 {len(elements)} 个包含'父规格'的元素:")
                for i, elem in enumerate(elements[:5], 1):  # 只显示前5个
                    print(f"   {i}. {elem.tag}: {elem.text}")
            else:
                print("   未找到任何包含'父规格'的元素")
                
            # 尝试查找页面上所有包含"规格"的元素
            print("\n🔍 查找页面上所有包含'规格'的元素...")
            elements = tab.eles("x://*[contains(text(), '规格')]")
            if elements:
                print(f"   找到 {len(elements)} 个包含'规格'的元素:")
                for i, elem in enumerate(elements[:5], 1):  # 只显示前5个
                    print(f"   {i}. {elem.tag}: {elem.text}")
            else:
                print("   未找到任何包含'规格'的元素")
        
        print(f"\n🎉 字段查找测试完成！")
        print("页面将保持打开状态供查看...")
        print("按 Ctrl+C 退出程序")
        
        # 保持页面打开
        try:
            while True:
                time.sleep(5)
                print("⏳ 页面仍然打开中...")
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_field_finding()
