"""
单选按钮组件

处理单选按钮字段
"""

from typing import Any, Dict

from loguru import logger

from .base_component import BaseComponent
from utils.response import ApiResponse, error_response, success_response


class RadioComponent(BaseComponent):
    """单选按钮组件"""

    def find_field_container(self, field_title: str):
        """
        查找字段容器 - 基于实际测试优化的精确策略

        Args:
            field_title: 字段标题

        Returns:
            字段容器元素或None
        """
        return None

    def fill_data(self, field_title: str, field_data: Dict[str, Any]) -> ApiResponse:
        """
        填充单选按钮字段
        
        Args:
            field_title: 字段标题
            field_data: 字段数据
            
        Returns:
            ApiResponse: 操作结果
        """
        try:
            value = field_data.get("value")
            # 查找并选择单选按钮
            field_container = self.tab.ele(f"x://div[@data-testid='beast-core-radioGroup' and .//div[text()='{value}']]")
            if not field_container:
                error_msg = f"未找到字段容器: {field_title}"
                self.log_operation(field_title, "查找字段容器", False, error_msg)
                return error_response(error_msg)

            radio_selected = self._select_radio_option(field_container, value, field_title)
            if not radio_selected:
                error_msg = f"未找到单选按钮选项: {value}"
                self.log_operation(field_title, "选择单选按钮", False, error_msg)
                return error_response(error_msg)
            
            self.log_operation(field_title, f"选择单选按钮: {value}")
            return success_response(f"成功填充字段 '{field_title}': {value}")
            
        except Exception as e:
            error_msg = f"填充单选按钮字段失败: {str(e)}"
            self.log_operation(field_title, "填充数据", False, error_msg)
            return error_response(error_msg)
    
    def _select_radio_option(self, field_container, value: str, field_title: str) -> bool:
        """
        选择单选按钮选项 - 使用经过测试验证的精确xpath

        Args:
            field_container: 字段容器元素
            value: 要选择的值
            field_title: 字段标题

        Returns:
            是否成功选择
        """
        try:
            # 使用经过实际测试验证的精确xpath选择器
            selector = f"x:.//label[@data-testid='beast-core-radio' and contains(.//text(), '{value}')]"

            option_element = field_container.ele(selector, timeout=5)

            if not option_element:
                logger.error(f"未找到单选按钮选项: {value}")
                return False

            for _ in range(3):
                # 检查当前选中状态
                current_checked = option_element.attr('data-checked') == 'true'
                logger.debug(f"选项 '{value}' 当前状态: {'已选中' if current_checked else '未选中'}")

                # 如果已经选中，无需重复点击
                if current_checked:
                    logger.info(f"选项 '{value}' 已经选中，无需重复操作")
                    return True

                # 使用JavaScript点击选项（普通点击在某些情况下无效）
                self.tab.run_js("arguments[0].click();", option_element)
                self.tab.wait(1)

                result = self._verify_radio_selection(field_container, value)
                if not result:
                    continue

                return True
            else:
                return False

        except Exception as e:
            logger.error(f"选择单选按钮选项失败: {value} -> {str(e)}")
            return False


    def _verify_radio_selection(self, field_container, expected_value: str) -> bool:
        """
        验证单选按钮选择结果 - 基于实际测试的简化验证

        Args:
            field_container: 字段容器元素
            expected_value: 期望选中的值

        Returns:
            是否验证成功
        """
        try:
            # 查找期望选中的选项
            expected_selector = f"x:.//label[@data-testid='beast-core-radio' and contains(.//text(), '{expected_value}')]"
            expected_option = field_container.ele(expected_selector, timeout=3)

            if not expected_option:
                logger.error(f"验证失败: 未找到期望选项 '{expected_value}'")
                return False

            # 检查是否选中
            is_checked = expected_option.attr('data-checked') == 'true'

            if is_checked:
                # logger.success(f"验证成功: 选项 '{expected_value}' 已选中")
                return True
            else:
                logger.error(f"验证失败: 选项 '{expected_value}' 未选中")
                return False

        except Exception as e:
            logger.error(f"验证单选按钮选择失败: {expected_value} -> {str(e)}")
            return False
