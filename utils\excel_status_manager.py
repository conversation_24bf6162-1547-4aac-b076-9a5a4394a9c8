"""
Excel状态管理工具

用于管理Excel文件中的处理状态列，包括：
1. 初始化状态列
2. 检查状态列是否存在
3. 添加状态列到现有Excel文件
"""

from pathlib import Path
from loguru import logger
from .excel import Excel


class ExcelStatusManager:
    """Excel状态管理器"""
    
    STATUS_COLUMNS = {
        "处理状态": "",  # 成功/失败/处理中
        "处理结果": "",  # 详细的成功消息或失败原因
        "处理时间": ""   # 处理时间戳
    }
    
    @staticmethod
    def ensure_status_columns(excel_path: str) -> bool:
        """
        确保Excel文件包含状态列，如果不存在则添加
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            bool: 操作是否成功
        """
        try:
            excel_file = Path(excel_path)
            if not excel_file.exists():
                logger.error(f"Excel文件不存在: {excel_path}")
                return False

            # 使用新的Excel类
            excel = Excel(excel_path)

            # 使用ensure_columns方法添加状态列
            result = excel.ensure_columns(ExcelStatusManager.STATUS_COLUMNS)

            if result:
                logger.info(f"已为 {excel_path} 确保状态列存在")
                return True
            else:
                logger.info(f"Excel文件 {excel_path} 已包含所有状态列")
                return True
                
        except Exception as e:
            logger.error(f"处理Excel状态列失败: {excel_path} - {str(e)}")
            return False
    
    @staticmethod
    def check_status_columns(excel_path: str) -> dict:
        """
        检查Excel文件的状态列情况
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            dict: 状态检查结果
        """
        try:
            excel_file = Path(excel_path)
            if not excel_file.exists():
                return {
                    "exists": False,
                    "error": f"文件不存在: {excel_path}"
                }
            
            # 使用新的Excel类
            excel = Excel(excel_path)
            data = excel.load()

            # 检查状态列
            existing_columns = []
            missing_columns = []

            if data:
                first_row_keys = set(data[0].keys())
                for col_name in ExcelStatusManager.STATUS_COLUMNS.keys():
                    if col_name in first_row_keys:
                        existing_columns.append(col_name)
                    else:
                        missing_columns.append(col_name)
            else:
                missing_columns = list(ExcelStatusManager.STATUS_COLUMNS.keys())

            return {
                "exists": True,
                "total_rows": len(data),
                "existing_columns": existing_columns,
                "missing_columns": missing_columns,
                "needs_update": len(missing_columns) > 0
            }
            
        except Exception as e:
            return {
                "exists": False,
                "error": f"检查失败: {str(e)}"
            }
    
    @staticmethod
    def get_processing_statistics(excel_path: str) -> dict:
        """
        获取处理统计信息
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            dict: 统计信息
        """
        try:
            excel_file = Path(excel_path)
            if not excel_file.exists():
                return {"error": f"文件不存在: {excel_path}"}
            
            # 使用新的Excel类
            excel = Excel(excel_path)

            # 使用get_column_statistics方法获取处理状态统计
            stats = excel.get_column_statistics("处理状态")

            if "error" in stats:
                return stats

            # 计算统计信息
            total_rows = stats["total_rows"]
            status_counts = stats["value_counts"]
            success_count = status_counts.get("成功", 0)
            failed_count = status_counts.get("失败", 0)
            pending_count = total_rows - success_count - failed_count

            return {
                "total_rows": total_rows,
                "success_count": success_count,
                "failed_count": failed_count,
                "pending_count": pending_count,
                "success_rate": round(success_count / total_rows * 100, 2) if total_rows > 0 else 0,
                "status_details": status_counts
            }
            
        except Exception as e:
            return {"error": f"获取统计信息失败: {str(e)}"}
    
    @staticmethod
    def reset_status(excel_path: str, status_filter: str = None) -> bool:
        """
        重置处理状态
        
        Args:
            excel_path: Excel文件路径
            status_filter: 要重置的状态类型（如"失败"），None表示重置所有
            
        Returns:
            bool: 操作是否成功
        """
        try:
            excel_file = Path(excel_path)
            if not excel_file.exists():
                logger.error(f"Excel文件不存在: {excel_path}")
                return False
            
            # 使用新的Excel类
            excel = Excel(excel_path)
            data = excel.load()

            if not data:
                logger.error("Excel文件为空")
                return False

            if "处理状态" not in data[0]:
                logger.error("Excel文件缺少'处理状态'列")
                return False

            # 重置状态
            reset_count = 0
            if status_filter:
                # 只重置指定状态的行
                for row in data:
                    if row.get("处理状态") == status_filter:
                        row["处理状态"] = ""
                        row["处理结果"] = ""
                        row["处理时间"] = ""
                        reset_count += 1
                logger.info(f"已重置 {reset_count} 行状态为'{status_filter}'的记录")
            else:
                # 重置所有状态
                for row in data:
                    row["处理状态"] = ""
                    row["处理结果"] = ""
                    row["处理时间"] = ""
                reset_count = len(data)
                logger.info(f"已重置所有 {reset_count} 行记录的状态")

            # 保存文件
            excel.write(data)
            return True
            
        except Exception as e:
            logger.error(f"重置状态失败: {excel_path} - {str(e)}")
            return False


def main():
    """命令行工具主函数"""
    import sys
    import argparse
    
    parser = argparse.ArgumentParser(description="Excel状态管理工具")
    parser.add_argument("excel_path", help="Excel文件路径")
    parser.add_argument("--init", action="store_true", help="初始化状态列")
    parser.add_argument("--check", action="store_true", help="检查状态列")
    parser.add_argument("--stats", action="store_true", help="显示统计信息")
    parser.add_argument("--reset", help="重置状态（可选：指定状态类型如'失败'）")
    
    args = parser.parse_args()
    
    if args.init:
        success = ExcelStatusManager.ensure_status_columns(args.excel_path)
        if success:
            print("✅ 状态列初始化成功")
        else:
            print("❌ 状态列初始化失败")
            sys.exit(1)
    
    elif args.check:
        result = ExcelStatusManager.check_status_columns(args.excel_path)
        if result.get("exists"):
            print(f"📊 Excel文件状态:")
            print(f"   总行数: {result['total_rows']}")
            print(f"   已有状态列: {result['existing_columns']}")
            print(f"   缺失状态列: {result['missing_columns']}")
            print(f"   需要更新: {'是' if result['needs_update'] else '否'}")
        else:
            print(f"❌ {result.get('error')}")
            sys.exit(1)
    
    elif args.stats:
        result = ExcelStatusManager.get_processing_statistics(args.excel_path)
        if "error" in result:
            print(f"❌ {result['error']}")
            sys.exit(1)
        else:
            print(f"📈 处理统计:")
            print(f"   总行数: {result['total_rows']}")
            print(f"   成功: {result['success_count']} ({result['success_rate']}%)")
            print(f"   失败: {result['failed_count']}")
            print(f"   待处理: {result['pending_count']}")
    
    elif args.reset is not None:
        status_filter = args.reset if args.reset else None
        success = ExcelStatusManager.reset_status(args.excel_path, status_filter)
        if success:
            print("✅ 状态重置成功")
        else:
            print("❌ 状态重置失败")
            sys.exit(1)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
