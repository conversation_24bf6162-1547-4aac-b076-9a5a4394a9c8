# 🎯 Temu 自动化工具

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.8+-green.svg)
![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)
![License](https://img.shields.io/badge/license-Educational-orange.svg)

**功能强大的 Temu 平台自动化工具，支持用户管理、商品创建、批量操作等功能**

[快速开始](#-快速开始) • [功能特性](#-功能特性) • [跨平台打包](#-跨平台打包) • [文档](#-文档)

</div>

---

## 🌟 功能特性

- 🎨 **美观的中文菜单界面** - 直观易用的图形化操作界面
- 👥 **智能用户管理** - 支持多用户导入、选择、登录管理
- 📦 **自动化商品创建** - 批量处理商品数据，自动化上传流程
- 🔐 **安全的数据处理** - 手机号隐私保护，安全的密码管理
- 🎁 **跨平台打包** - 支持 Windows、macOS、Linux 平台打包
- 🛡️ **完善的错误处理** - 友好的错误提示和异常恢复机制
- 📊 **实时状态显示** - 清晰的操作状态和进度反馈

## 📋 目录

- [快速开始](#-快速开始)
- [功能特性](#-功能特性)
- [安装指南](#-安装指南)
- [配置说明](#️-配置说明)
- [使用方法](#-使用方法)
- [跨平台打包](#-跨平台打包)
- [项目结构](#-项目结构)
- [开发指南](#️-开发指南)
- [故障排除](#-故障排除)
- [贡献指南](#-贡献指南)

## 🚀 快速开始

### 方法一：使用 Windows 可执行文件（推荐）

1. **下载发布版本**：从 [Releases](../../releases) 下载最新的 `TemuAutoTool.exe`
2. **运行程序**：双击 `TemuAutoTool.exe` 启动
3. **导入用户**：选择菜单中的"导入用户"功能
4. **开始使用**：选择用户登录，开始自动化操作

### 方法二：从源码运行

```bash
# 1. 克隆项目
git clone <repository-url>
cd dp_temu

# 2. 安装依赖
pip install -r requirements.txt

# 3. 运行程序
python index.py
```

## 📦 安装指南

### 系统要求

| 项目 | 要求 | 下载地址 |
|------|------|----------|
| **操作系统** | Windows 10+, macOS 10.14+, Linux | - |
| **Python 版本** | Python 3.12.10 (推荐) | [下载链接](https://www.python.org/ftp/python/3.12.10/python-3.12.10-amd64.exe) |
| **内存** | 至少 4GB RAM | - |
| **磁盘空间** | 至少 2GB 可用空间 | - |
| **浏览器** | Chrome 最新版本 | [下载链接](https://www.google.com/intl/zh-CN/chrome/) |

> **注意**：使用exe版本无需安装Python，Chrome浏览器是必需的。

### 环境检查

```bash
# 检查 Python 版本
python --version

# 检查 Chrome 浏览器
google-chrome --version
# 或 chromium --version

# 检查网络连接
curl -I https://seller.kuajingmaihuo.com
```

### 详细安装步骤

#### 1. 获取项目代码
```bash
# 方法一：Git 克隆
git clone <repository-url>
cd dp_temu

# 方法二：下载 ZIP 包并解压
```

#### 2. 创建虚拟环境（推荐）
```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate
```

#### 3. 安装依赖包
```bash
# 升级 pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

#### 4. 验证安装
```bash
# 检查安装是否成功
python index.py --help

# 启动菜单界面
python index.py
```

## ⚙️ 配置说明

### 用户数据配置（必需）

#### 1. 准备用户数据文件
```bash
# 复制模板文件
cp data/users.csv.copy data/users.csv
```

#### 2. 编辑用户信息
编辑 `data/users.csv` 文件：
```csv
index,phone,password,username
1,13800138000,your_password,用户1
2,13800138001,your_password,用户2
3,13800138002,your_password,用户3
```

**字段说明**：
- `index`: 用户序号（唯一标识）
- `phone`: 登录手机号
- `password`: 登录密码
- `username`: 显示名称

### 环境变量配置（可选）

#### 1. 创建环境配置文件
```bash
# 如果存在模板文件
cp .env.example .env
```

#### 2. 配置环境变量
```bash
# 浏览器配置
USER_DATA_PATH="/path/to/browser/data"

# 商品数据路径
GOODS_DATA_PATH="/path/to/goods/data/"

# 邮件通知（可选）
SMTP_SERVER="smtp.example.com"
SENDER_EMAIL="<EMAIL>"
```

### 图片格式配置

#### 支持的图片格式
- **推荐格式**：`.jpg`, `.jpeg`, `.png`
- **扩展格式**：`.gif`, `.bmp`, `.webp`（部分功能支持）

#### 图片命名规范
商品图片必须按以下格式命名：
```
SKU号-序号.扩展名
例如：HZ-M090-1.jpg, HZ-M090-2.png, HZ-M090-3.jpeg
```

#### 图片要求
- **数量**：每个商品必须有5张图片（序号1-5）
- **大小**：单个文件不超过10MB
- **格式**：支持混合格式（如1.png, 2.jpg, 3.jpeg等）

### 浏览器配置

- **自动端口分配**：9001, 9002, 9003...（基于用户索引）
- **独立配置文件**：每个用户使用独立的浏览器配置
- **端口检查**：确保端口未被占用

## 📖 使用方法

### 图形界面模式（推荐）

#### 启动菜单界面
```bash
# 直接运行，进入中文菜单界面
python index.py
```

#### 菜单功能
- **📥 导入用户**：从 CSV 文件导入用户数据
- **🔐 选择用户登录**：选择用户并执行登录
- **📦 上传商品**：批量处理和上传商品数据
- **🚪 登出**：安全退出当前用户

### 命令行模式

#### 基本命令
```bash
# 查看帮助
python index.py --help

# 用户登录
python index.py login          # 交互式选择
python index.py login 1        # 直接指定用户

# 创建商品
python index.py cg             # 交互式创建
python index.py cg 1 /path     # 指定用户和路径

# 查看用户列表
python index.py users
```

#### 命令参考

| 命令 | 描述 | 示例 |
|------|------|------|
| `login [INDEX]` | 用户登录 | `python index.py login 1` |
| `cg [INDEX] [PATH]` | 创建商品 | `python index.py cg 1 /data` |
| `users` | 显示用户列表 | `python index.py users` |

### 完整使用流程

#### 首次使用
1. **准备用户数据**：编辑 `data/users.csv` 文件
2. **启动程序**：运行 `python index.py`
3. **导入用户**：选择"导入用户"功能
4. **选择登录**：选择用户并登录
5. **上传商品**：准备商品数据并上传

#### 日常使用
1. **启动程序**：`python index.py`
2. **选择用户**：从列表中选择用户登录
3. **处理商品**：上传或管理商品数据
4. **安全退出**：使用"登出"功能

## 🎁 跨平台打包

### 快速打包

#### Windows
```cmd
# 在项目根目录运行
build.bat
```

#### macOS / Linux
```bash
# 在项目根目录运行
./build.sh

# 或者使用 macOS 专用脚本
./packaging/build_macos.sh
```

#### 通用方式（所有平台）
```bash
# 使用 Python 跨平台脚本
python3 build.py
```

### 打包结果

#### Windows
- **可执行文件**：`release/TemuAutoTool.exe`
- **配置文件**：`release/config/`
- **数据模板**：`release/data/users.csv.copy`
- **使用说明**：`release/使用说明.txt`

#### macOS / Linux
- **可执行文件**：`release/TemuAutoTool`
- **配置文件**：`release/config/`
- **数据模板**：`release/data/users.csv.copy`
- **使用说明**：`release/使用说明.txt`

### 详细说明
- 📁 打包配置：`packaging/` 目录
- 📖 详细文档：`packaging/README.md`
- 🎯 用户指南：`docs/user-guides/`

### 系统要求

#### 所有平台
- 无需安装 Python（运行时）
- 独立运行，包含所有依赖

#### Windows
- Windows 10/11

#### macOS
- macOS 10.14+

#### Linux
- Ubuntu 18.04+ / CentOS 7+

## 📁 项目结构

```
dp_temu/
├── 📄 README.md              # 项目说明文档
├── 🚀 build.bat              # 快速打包脚本
├── 🎯 index.py               # 主程序入口
├── 🎨 app.py                 # 菜单应用界面
├── ⚙️ pyproject.toml         # 项目配置
├── 📦 requirements.txt       # 开发依赖列表
│
├── 📁 biz/                   # 核心业务逻辑
│   ├── goods.py             # 商品管理模块
│   ├── temu.py              # Temu 平台接口
│   └── user.py              # 用户管理模块
│
├── 🛠️ utils/                 # 工具函数库
│   ├── chrome.py            # 浏览器控制
│   ├── common.py            # 通用工具
│   ├── user_manager.py      # 用户管理器
│   ├── excel.py             # Excel 处理
│   └── ...                  # 其他工具
│
├── 🧩 components/            # UI 组件库
│   ├── base_component.py    # 基础组件
│   ├── input.py             # 输入组件
│   ├── select.py            # 选择组件
│   └── ...                  # 其他组件
│
├── 📊 data/                  # 数据文件
│   ├── users.csv            # 用户数据
│   └── users.csv.copy       # 用户数据模板
│
├── ⚙️ config/                # 配置文件
│   └── fileds.json          # 字段配置
│
├── 📦 packaging/             # 打包相关文件
│   ├── README.md            # 打包说明
│   ├── requirements-windows.txt
│   ├── temu_app.spec        # PyInstaller 配置
│   └── build_windows.bat    # 打包脚本
│
├── 📚 docs/                  # 文档目录
│   └── user-guides/         # 用户指南
│       ├── 完整菜单系统使用说明.md
│       └── 部署说明.md
│
├── 🧪 examples/              # 示例代码
└── 🔬 tests/                 # 测试文件
```

## 🛠️ 开发指南

### 开发环境设置

```bash
# 1. 克隆项目
git clone <repository-url>
cd dp_temu

# 2. 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

# 3. 安装开发依赖
pip install -r requirements.txt
pip install black isort pytest  # 开发工具

# 4. 运行测试
python -m pytest tests/
```

### 代码规范

#### 格式化工具
```bash
# 代码格式化
black .                    # 格式化代码
isort .                    # 整理导入

# 一键格式化
isort . && black .
```

#### 提交前检查
```bash
# 运行测试
pytest

# 检查代码格式
black --check .
isort --check-only .
```

### 项目架构

- **`biz/`**: 核心业务逻辑，包含 Temu 平台交互
- **`utils/`**: 通用工具函数，浏览器控制等
- **`components/`**: UI 组件库，可复用的界面元素
- **`app.py`**: 主应用界面，菜单系统
- **`index.py`**: 程序入口，命令行接口

## 🔍 故障排除

### 常见问题

#### 🚫 程序无法启动
```bash
# 检查 Python 版本
python --version

# 检查依赖安装
pip list | grep -E "(click|loguru|DrissionPage)"

# 重新安装依赖
pip install -r requirements.txt
```

#### 🌐 浏览器相关问题
```bash
# 检查 Chrome 安装
google-chrome --version

# 检查端口占用
netstat -ano | findstr :9001  # Windows
lsof -i :9001                 # macOS/Linux

# 清理浏览器数据
rm -rf ~/.config/google-chrome/Default  # Linux
```

#### 📁 文件权限问题
```bash
# 检查文件权限
ls -la data/users.csv

# 修复权限
chmod 644 data/users.csv
```

#### 🔗 网络连接问题
```bash
# 测试网络连接
curl -I https://seller.kuajingmaihuo.com

# 检查代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

### 调试模式

#### 方法一：通过菜单界面切换
```bash
# 启动程序
python index.py

# 在主菜单中选择 "🔧 调试模式切换"
# 可以实时启用/禁用DEBUG日志
```

#### 方法二：通过环境变量
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
python index.py

# 或者使用DEBUG开关
export DEBUG=1
python index.py

# 查看日志文件
tail -f logs/app.log
```

#### 方法三：通过.env文件
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑.env文件，设置日志级别
# LOG_LEVEL=DEBUG  # 启用调试模式
# LOG_LEVEL=INFO   # 禁用调试模式（默认）
```

#### 日志级别说明
- **INFO**（默认）：显示重要操作信息，隐藏调试细节
- **DEBUG**：显示详细的调试信息，包括组件内部操作
- **WARNING**：只显示警告和错误信息
- **ERROR**：只显示错误信息

## 📚 文档

- 📋 [完整功能清单](docs/FEATURE_LIST.md) - V1.0.0所有功能详细说明
- 📖 [部署指南](docs/DEPLOYMENT_GUIDE.md) - 详细的部署和使用说明
- ✅ [部署检查清单](docs/DEPLOYMENT_CHECKLIST.md) - 部署前检查要点
- 🔧 [构建改进说明](docs/BUILD_IMPROVEMENTS.md) - 开发和构建相关信息
- 📖 [完整菜单系统使用说明](docs/user-guides/完整菜单系统使用说明.md)
- 🚀 [部署说明](docs/user-guides/部署说明.md)
- 📦 [Windows 打包说明](packaging/README.md)

## 🤝 贡献指南

### 贡献流程
1. **Fork 项目** 到您的 GitHub 账号
2. **创建功能分支** `git checkout -b feature/amazing-feature`
3. **提交更改** `git commit -m 'Add amazing feature'`
4. **推送分支** `git push origin feature/amazing-feature`
5. **创建 Pull Request**

### 贡献规范
- 遵循现有代码风格
- 添加必要的测试
- 更新相关文档
- 确保所有测试通过

## ⚠️ 免责声明

- 本项目仅供学习和研究使用
- 请遵守 Temu 平台的使用条款
- 使用者需自行承担使用风险
- 合理控制自动化操作频率

## 📞 技术支持

### 获取帮助
1. 📖 查看文档和故障排除部分
2. 🔍 搜索已有的 [Issues](../../issues)
3. 💬 创建新的 Issue 描述问题
4. 📧 联系项目维护者

### 反馈建议
- 🐛 Bug 报告：使用 Bug Report 模板
- 💡 功能建议：使用 Feature Request 模板
- 📝 文档改进：直接提交 PR

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给它一个 Star！**

Made with ❤️ by the development team

</div>
