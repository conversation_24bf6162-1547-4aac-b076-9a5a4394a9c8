import csv
import sys
from pathlib import Path
from typing import Dict, List, Optional

import click
from loguru import logger

from utils.common import get_project_root_path


class UserManager:
    """用户管理工具类，负责读取和管理users.csv文件"""
    
    def __init__(self, csv_path: Optional[str] = None):
        """
        初始化用户管理器

        Args:
            csv_path: CSV文件路径，如果为None则使用默认路径
        """
        if csv_path is None:
            # 智能路径解析，兼容开发环境和打包环境
            csv_path = self._get_default_csv_path()

        self.csv_path = Path(csv_path)
        self.users: List[Dict[str, str]] = []

    def _get_default_csv_path(self) -> Path:
        """
        获取默认的CSV文件路径，兼容开发环境和打包环境

        Returns:
            Path: CSV文件路径
        """
        if getattr(sys, 'frozen', False):
            # 打包环境：可执行文件所在目录
            app_dir = Path(sys.executable).parent
            logger.debug(f"打包环境，应用目录: {app_dir}")
        else:
            # 开发环境：项目根目录
            app_dir = Path(get_project_root_path())
            logger.debug(f"开发环境，项目根目录: {app_dir}")

        csv_path = app_dir / "data" / "users.csv"
        logger.debug(f"默认CSV路径: {csv_path}")

        # 确保data目录存在
        csv_path.parent.mkdir(exist_ok=True)

        return csv_path
        
    def load_users(self) -> bool:
        """
        加载用户数据

        Returns:
            bool: 是否成功加载
        """
        try:
            logger.debug(f"尝试加载用户文件: {self.csv_path}")
            logger.debug(f"文件绝对路径: {self.csv_path.absolute()}")
            logger.debug(f"当前工作目录: {Path.cwd()}")

            if not self.csv_path.exists():
                # logger.error(f"用户文件不存在: {self.csv_path.absolute()}")
                logger.error(f"用户数据未导入, 请先导入用户数据")
                return False

            # 检查文件大小
            file_size = self.csv_path.stat().st_size
            logger.debug(f"文件大小: {file_size} 字节")

            if file_size == 0:
                logger.error(f"用户文件为空: {self.csv_path}")
                return False

            with open(self.csv_path, 'r', encoding='utf-8-sig') as file:
                reader = csv.DictReader(file)
                self.users = []

                # 检查CSV头部
                if reader.fieldnames is None:
                    logger.error("CSV文件没有有效的头部行")
                    return False

                logger.debug(f"CSV字段名: {reader.fieldnames}")

                # 检查必需的字段
                required_fields = ['index', 'phone', 'password']
                missing_fields = [field for field in required_fields if field not in reader.fieldnames]
                if missing_fields:
                    logger.error(f"CSV文件缺少必需字段: {missing_fields}")
                    return False

                row_count = 0
                valid_count = 0

                for row in reader:
                    row_count += 1
                    logger.debug(f"处理第{row_count}行: {dict(row)}")

                    # 过滤掉空行或无效数据
                    index_val = row.get('index', '').strip()
                    phone_val = row.get('phone', '').strip()
                    password_val = row.get('password', '').strip()

                    if index_val and phone_val and password_val:
                        self.users.append(row)
                        valid_count += 1
                        logger.debug(f"第{row_count}行有效: index={index_val}, phone={phone_val[:3]}***")
                    else:
                        logger.debug(f"第{row_count}行无效: index='{index_val}', phone='{phone_val}', password={'有' if password_val else '无'}")

                logger.debug(f"总行数: {row_count}, 有效行数: {valid_count}")

            logger.info(f"成功加载 {len(self.users)} 个用户")
            return len(self.users) > 0

        except UnicodeDecodeError as e:
            logger.error(f"文件编码错误: {str(e)}")
            logger.error("请确保CSV文件使用UTF-8编码保存")
            return False
        except Exception as e:
            logger.error(f"加载用户文件失败: {str(e)}")
            logger.exception("详细错误信息:")
            return False
    
    def get_user_by_index(self, index: str) -> Optional[Dict[str, str]]:
        """
        根据index获取用户信息
        
        Args:
            index: 用户索引
            
        Returns:
            Dict[str, str]: 用户信息，如果未找到返回None
        """
        for user in self.users:
            if user.get('index') == str(index):
                return user
        return None
    
    def display_users(self) -> None:
        """显示所有可用用户"""
        if not self.users:
            click.echo("❌ 没有可用的用户数据")
            return
            
        click.echo("\n📋 可用用户列表:")
        click.echo("-" * 60)
        click.echo(f"{'序号':<6} {'手机号':<15} {'用户名':<15} {'状态'}")
        click.echo("-" * 60)
        
        for user in self.users:
            index = user.get('index', '')
            phone = user.get('phone', '')
            username = user.get('username', '')
            
            # 隐藏手机号中间4位
            masked_phone = self._mask_phone(phone)
            
            click.echo(f"{index:<6} {masked_phone:<15} {username:<15} {'可用' if phone and user.get('password') else '不可用'}")
        
        click.echo("-" * 60)
    
    def _mask_phone(self, phone: str) -> str:
        """
        隐藏手机号中间4位
        
        Args:
            phone: 原始手机号
            
        Returns:
            str: 隐藏后的手机号
        """
        if not phone or len(phone) < 7:
            return phone
            
        return phone[:3] + "****" + phone[-4:]
    
    def get_user_count(self) -> int:
        """获取用户数量"""
        return len(self.users)

    def get_user_display_name(self, index: str) -> str:
        """
        获取用户的显示名称

        Args:
            index: 用户索引

        Returns:
            str: 用户显示名称
        """
        user = self.get_user_by_index(index)
        if not user:
            return f"未知用户(序号:{index})"

        username = user.get('username', '未知用户')
        phone = user.get('phone', '')
        masked_phone = self._mask_phone(phone) if phone else '未知手机号'

        return f"{username} ({masked_phone})"
    
    def validate_index(self, index: str) -> bool:
        """
        验证index是否有效
        
        Args:
            index: 用户索引
            
        Returns:
            bool: 是否有效
        """
        return self.get_user_by_index(index) is not None
    
    def prompt_user_selection(self) -> Optional[str]:
        """
        提示用户选择账号（中文选择框界面）

        Returns:
            str: 选择的用户index，如果取消返回None
        """
        if not self.users:
            click.echo("❌ 没有可用的用户数据")
            return None

        return self._show_chinese_selection_menu()

    def _show_chinese_selection_menu(self) -> Optional[str]:
        """
        显示中文选择框菜单

        Returns:
            str: 选择的用户index，如果取消返回None
        """
        while True:
            try:
                # 清屏效果（可选，在终端中提供更好的体验）
                # click.clear()  # 注释掉清屏，避免在某些环境下出现问题

                # 显示标题
                click.echo("\n" + "🔹" * 35)
                click.echo("🎯  Temu 账号选择器")
                click.echo("🔹" * 35)
                click.echo(f"📊 共找到 {len(self.users)} 个可用账号\n")

                # 显示用户选项
                for i, user in enumerate(self.users, 1):
                    index = user.get('index', '')
                    phone = user.get('phone', '')
                    username = user.get('username', '')
                    masked_phone = self._mask_phone(phone)

                    # 更美观的状态显示
                    if phone and user.get('password'):
                        status_icon = "✅"
                        status_text = "可用"
                        status_color = "green"
                    else:
                        status_icon = "❌"
                        status_text = "不可用"
                        status_color = "red"

                    # 格式化显示
                    click.echo(f"  [{click.style(str(i), fg='cyan', bold=True)}] "
                              f"{click.style(username, fg='white', bold=True)} "
                              f"({click.style(masked_phone, fg='yellow')}) "
                              f"- {status_icon} {click.style(status_text, fg=status_color)}")

                click.echo(f"\n  [{click.style('0', fg='red', bold=True)}] "
                          f"{click.style('退出程序', fg='red')}")
                click.echo("🔹" * 35)

                # 获取用户选择
                choice = click.prompt(
                    f"💡 请输入选项编号 (1-{len(self.users)}, 0=退出)",
                    type=int,
                    show_default=False
                )

                # 处理用户选择
                if choice == 0:
                    click.echo(f"\n{click.style('👋 已退出程序', fg='yellow')}")
                    return None
                elif 1 <= choice <= len(self.users):
                    selected_user = self.users[choice - 1]
                    index = selected_user.get('index')
                    username = selected_user.get('username', '未知用户')
                    phone = selected_user.get('phone', '')

                    # 检查用户是否可用
                    if not phone or not selected_user.get('password'):
                        click.echo(f"\n{click.style('❌ 用户', fg='red')} "
                                  f"{click.style(username, fg='white', bold=True)} "
                                  f"{click.style('数据不完整，无法使用', fg='red')}")
                        click.echo(f"{click.style('按回车键继续...', fg='yellow')}")
                        try:
                            input()
                        except (EOFError, KeyboardInterrupt):
                            return None
                        continue

                    # 确认选择
                    masked_phone = self._mask_phone(phone)
                    click.echo(f"\n{click.style('✅ 您选择了:', fg='green')} "
                              f"{click.style(username, fg='white', bold=True)} "
                              f"({click.style(masked_phone, fg='yellow')})")

                    if click.confirm(f"{click.style('确认使用此账号吗？', fg='cyan')}", default=True):
                        click.echo(f"\n{click.style('🎉 已选择用户:', fg='green')} "
                                  f"{click.style(username, fg='white', bold=True)} "
                                  f"(序号: {click.style(index, fg='cyan')})")
                        return index
                    else:
                        continue
                else:
                    click.echo(f"\n{click.style('❌ 无效的选项:', fg='red')} {choice}")
                    click.echo(f"{click.style('请输入', fg='yellow')} "
                              f"{click.style(f'1-{len(self.users)}', fg='cyan')} "
                              f"{click.style('或', fg='yellow')} "
                              f"{click.style('0', fg='cyan')}")
                    click.echo(f"{click.style('按回车键继续...', fg='yellow')}")
                    try:
                        input()
                    except (EOFError, KeyboardInterrupt):
                        return None

            except click.Abort:
                click.echo(f"\n{click.style('👋 用户取消操作', fg='yellow')}")
                return None
            except (ValueError, TypeError):
                click.echo(f"\n{click.style('❌ 请输入有效的数字', fg='red')}")
                click.echo(f"{click.style('按回车键继续...', fg='yellow')}")
                try:
                    input()
                except (EOFError, KeyboardInterrupt):
                    return None
            except Exception as e:
                click.echo(f"\n{click.style('❌ 发生错误:', fg='red')} {str(e)}")
                click.echo(f"{click.style('按回车键继续...', fg='yellow')}")
                try:
                    input()
                except (EOFError, KeyboardInterrupt):
                    return None

    def prompt_user_selection_simple(self) -> Optional[str]:
        """
        简单的用户选择（保留原有方式作为备选）

        Returns:
            str: 选择的用户index，如果取消返回None
        """
        if not self.users:
            click.echo("❌ 没有可用的用户数据")
            return None

        self.display_users()

        while True:
            try:
                index = click.prompt("\n请输入要使用的账号序号", type=str)

                if self.validate_index(index):
                    user = self.get_user_by_index(index)
                    username = user.get('username', '未知用户')
                    click.echo(f"✅ 已选择用户: {username} (序号: {index})")
                    return index
                else:
                    click.echo(f"❌ 无效的序号: {index}，请重新输入")

            except click.Abort:
                click.echo("\n❌ 用户取消操作")
                return None
            except Exception as e:
                click.echo(f"❌ 输入错误: {str(e)}")
