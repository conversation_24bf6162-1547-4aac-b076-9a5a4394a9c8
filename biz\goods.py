from typing import Any, Dict

from DrissionPage import Chromium
from loguru import logger

from components import ComponentFactory
from utils.response import ApiResponse, error_response, success_response


class Goods:
    def __init__(self, page: Chromium):
        self.page = page
        self.home_url = None
        self.category_url = "https://seller.kuajingmaihuo.com/goods/product-create/category"

    def select_category(self, category_name: str) -> ApiResponse:
        """
        选择商品分类

        Args:
            category_name: 分类名称

        Returns:
            ApiResponse: 选择结果，成功时data包含商品编辑页面的URL
        """
        try:
            logger.info(f"开始选择商品分类: {category_name}")

            # 设置窗口最大化
            try:
                self.page.latest_tab.set.window.max()
            except Exception as _:
                pass

            # 打开分类选择页面
            tab = self.page.new_tab(self.category_url)
            tab.wait(3)

            if "login" in tab.url:
                logger.error("用户未登录，请先登录")
                return error_response("用户未登录，请先登录", code=1001)

            # 关闭可能的弹窗
            self._close_all_popups(tab)

            # 查找搜索输入框
            search_input = tab.ele("x://input[@placeholder='搜索分类：可输入商品名称']", timeout=10)
            if not search_input:
                return error_response("未找到分类搜索输入框")

            # 输入分类名称
            search_input.clear(True)
            search_input.input(category_name)
            tab.wait(2)

            # 查找包含关键词的分类项（使用span标签，因为从测试看到分类项是span）
            category_items = tab.eles(f"x://span[contains(@class, 'category_keywordLabel') and contains(., '{category_name}')]", timeout=5)
            if not category_items:
                return error_response(f"未找到匹配的分类: {category_name}")

            # 点击第一个匹配的分类项
            first_category = category_items[0]
            logger.info(f"选择分类: {first_category.text}")
            first_category.click()
            tab.wait(3)

            # 等待页面更新并查找下一步按钮（使用精确的选择器）
            next_button = tab.ele("x://button[.//span[text()='下一步']]", timeout=10)
            if not next_button or not next_button.states.is_displayed:
                return error_response("未找到下一步按钮")

            next_button.click()
            tab.wait(3)

            # 检查是否成功跳转到商品编辑页面
            if "product-edit" in tab.url:
                self.home_url = tab.url
                logger.info(f"成功选择分类 '{category_name}' 并跳转到商品编辑页面")
                return success_response(f"成功选择分类: {category_name}", data={"url": tab.url})
            else:
                return error_response("分类选择后未能跳转到商品编辑页面")

        except Exception as e:
            logger.error(f"选择商品分类失败: {str(e)}")
            return error_response(f"选择商品分类失败: {str(e)}")

    def create(self, goods_data: dict[str, Any]) -> ApiResponse:
        """创建商品

        Args:
            goods_data: 商品数据

        Returns
        -------
            ApiResponse: 标准化响应对象，包含 code, message, success, data 字段
        """
        try:
            # goods_draft_id = goods_data.get("goods_draft_id")
            # if not goods_draft_id:
            #     return error_response("商品分类ID不能为空",  data={"missing_field": "goods_draft_id"})

            if not self.home_url:
                return error_response("请先选择商品获取到商品上传的url")

            tab = self.page.new_tab(self.home_url)
            tab.wait(3)

            logger.debug(f"创建商品: {goods_data}")

            if "login" in tab.url:
                logger.info("用户未登录，请先登录")
                return error_response("用户未登录，请先登录", code=1001)

            # 关闭所有弹窗
            self._close_all_popups(tab)

            # 删除弹窗元素
            try:
                portal = tab.ele("x://div[@data-testid='beast-core-portal-main']", timeout=3)
                if portal:
                    tab.run_js("arguments[0].remove();", portal)
            except Exception as e:
                pass

            # 填充商品数据
            fill_result = self._fill_goods_data(tab, goods_data)
            if not fill_result.success:
                return fill_result

            # # 点击创建订单
            # create_button = tab.ele("x://button[.='创建订单']", timeout=10)
            # if not create_button:
            #     return error_response("未找到创建订单按钮")
            # create_button.click()
            # tab.wait(1)

            if "site-main" in tab.url:
                logger.info("商品创建流程完成")

            return success_response("创建商品成功")
        except Exception as e:
            logger.error(f"创建商品失败: {str(e)}")
            return error_response(f"创建商品失败: {str(e)}")

    @staticmethod
    def _fill_goods_data(tab, goods_data: dict[str, Any]) -> ApiResponse:
        """
        填充商品数据到表单

        Args:
            tab: 浏览器标签页对象
            goods_data: 商品数据

        Returns:
            ApiResponse: 填充结果
        """
        try:
            logger.info("开始填充商品数据")

            # 等待页面加载完成
            tab.wait(3)

            # 遍历所有字段数据
            for field_title, field_data in goods_data.items():
                if not isinstance(field_data, dict):
                    continue

                if field_title == "goods_draft_id" or field_title == "货号":
                    continue

                # 获取字段类型
                field_type = field_data.get("type")
                if not field_type:
                    logger.warning(f"字段 '{field_title}' 没有指定类型，跳过处理")
                    continue

                # 检查是否支持该组件类型
                if not ComponentFactory.is_supported(field_type):
                    logger.warning(f"不支持的组件类型: {field_type}，字段: {field_title}")
                    continue

                # 创建组件并填充数据
                component = ComponentFactory.create_component(field_type, tab)
                if component:
                    result = component.fill_data(field_title, field_data)
                    if result.success == False:
                        logger.error(f"字段 '{field_title}' 填充失败: {result.message}")
                        # 如果是必填字段失败，直接返回错误
                        if field_data.get("require", 0) == 1:
                            return result
                else:
                    logger.error(f"无法创建组件: {field_type}，字段: {field_title}")
                    if field_data.get("require", 0) == 1:
                        return error_response(f"无法创建组件处理必填字段: {field_title}")

            logger.info("商品数据填充完成")

            # 选中checkbox 我已阅读并同意
            checkbox_ele = tab.ele("x://div[contains(@class, 'product-create_newButtonContainer')]//label[@data-testid='beast-core-checkbox']")
            if not checkbox_ele:
                return error_response("找不到 我已阅读并同意 勾选框")

            checkbox_ele.click()
            tab.wait(1)

            tab.listen.start("bg-visage-mms/product/add")
            create_ele = tab.ele("x://button[.='创建']")
            if not create_ele:
                return error_response("找不到 创建 按钮")

            create_ele.click()
            res = tab.listen.wait(timeout=30)
            if res and res.response and res.response.body:
                body = res.response.body
                success = body.get("success")
                if success:
                    logger.success("添加商品数据提交成功...")
                    return success_response("商品数据填充成功")
                else:
                    error_msg = body.get("errorMsg")
                    return error_response(f"商品数据添加失败: {error_msg}")

            return error_response("商品数据添加失败")

        except Exception as e:
            logger.error(f"填充商品数据失败: {str(e)}")
            return error_response(f"填充商品数据失败: {str(e)}")

    @staticmethod
    def _close_all_popups(tab):
        """
        关闭页面上的所有弹窗

        Args:
            tab: 浏览器标签页对象
        """
        logger.info("开始关闭页面弹窗")

        # 等待页面初始加载
        tab.wait(3)

        # 定义各种可能的关闭按钮选择器
        close_selectors = [
            "x://*[local-name()='svg' and @data-testid='beast-core-modal-icon-close']",
            "x://div[@data-testid='beast-core-modal-inner']//*[local-name()='svg' and @data-testid='beast-core-icon-close']",
            "x://div[.='全部消息']/../..//*[local-name()='svg' and @data-testid='beast-core-icon-close']"
        ]

        # 尝试关闭弹窗，最多尝试10次
        max_attempts = 10
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            found_popup = False

            logger.info(f"第 {attempt} 次尝试关闭弹窗")

            for selector in close_selectors:
                try:
                    # 查找所有匹配的关闭按钮
                    close_buttons = tab.eles(selector, timeout=2)

                    for close_btn in close_buttons:
                        try:
                            # 检查元素是否可见和可点击
                            if close_btn.states.is_displayed and close_btn.states.is_enabled:
                                logger.info(f"找到并点击关闭按钮: {selector}")
                                close_btn.click()
                                tab.wait(1)  # 等待弹窗关闭动画
                                found_popup = True
                                break
                        except Exception as e:
                            logger.debug(f"点击关闭按钮失败: {str(e)}")
                            continue

                    if found_popup:
                        break

                except Exception as e:
                    logger.debug(f"查找关闭按钮失败: {selector} -> {str(e)}")
                    continue

            # 如果没有找到更多弹窗，退出循环
            if not found_popup:
                logger.info("没有找到更多弹窗，关闭完成")
                break

            # 等待一下再继续查找
            tab.wait(1)

        # 额外等待确保所有弹窗都已关闭
        tab.wait(2)
        logger.info("弹窗关闭流程完成")
