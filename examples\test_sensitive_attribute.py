"""
敏感属性和尺寸重量字段综合测试

测试以下字段的功能：
1. 敏感属性选择框
2. 最长边数字输入
3. 次长边数字输入
4. 最短边数字输入
5. 重量数字输入

先填充ProductSKC数据，然后测试所有相关字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from biz.temu import Temu
from components.product_skc import ProductSkcComponent
from components.dropdown_select import DropdownSelectComponent
from components import ComponentFactory


def fill_product_skc_data(tab):
    """填充ProductSKC数据"""

    print("\n🔧 创建ProductSkcComponent实例...")
    product_skc_component = ProductSkcComponent(tab)

    # ProductSKC测试数据
    test_cases = [
        ("父规格1", {"value": "颜色", "require": 0}),
        ("商品规格1", {"value": "红色", "require": 0}),
    ]

    print("\n📝 开始填充ProductSKC数据...")
    for field_title, data in test_cases:
        print(f"\n🎯 填充: {field_title} = {data['value']}")

        result = product_skc_component.fill_data(field_title, data)

        if result.success:
            print(f"✅ {field_title} 成功: {result.message}")
        else:
            print(f"❌ {field_title} 失败: {result.message}")

        tab.wait(3)

    print(f"\n🎉 ProductSKC数据填充完成！")

    # 等待SKC数据处理完成，让页面更新
    print("⏳ 等待SKC数据处理完成...")
    tab.wait(5)

    # 点击页面空白区域确保数据保存
    print("💾 确保SKC数据保存...")
    try:
        tab.run_js("document.body.click();")
        tab.wait(3)
    except Exception as e:
        print(f"点击页面异常: {e}")

    print("✅ SKC数据处理完成，字段应该已解锁")
    return True


def test_sensitive_attribute_select():
    """测试敏感属性和尺寸重量字段"""

    print("🎯 测试敏感属性和尺寸重量字段")
    print("=" * 50)

    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False

        page = temu.page
        print("✅ 登录成功")

        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")

        # 3. 先填充ProductSKC数据
        if not fill_product_skc_data(tab):
            print("❌ ProductSKC数据填充失败")
            return False

        # 4. 创建敏感属性选择组件实例
        print("\n🔧 创建敏感属性选择组件实例...")
        component = DropdownSelectComponent(tab)

        # 5. 测试敏感属性和尺寸重量相关字段
        test_fields = [
            {
                "title": "敏感属性",
                "type": "dropdown_select",
                "require": 1,
                "value": "否"
            },
            {
                "title": "最长边",
                "type": "input_number",
                "require": 1,
                "value": "25.5"
            },
            {
                "title": "次长边",
                "type": "input_number",
                "require": 1,
                "value": "15.8"
            },
            {
                "title": "最短边",
                "type": "input_number",
                "require": 1,
                "value": "8.2"
            },
            {
                "title": "重量",
                "type": "input_in_table",
                "require": 1,
                "value": "150"
            }
        ]

        print(f"\n📝 开始测试敏感属性和尺寸重量字段...")
        for field_config in test_fields:
            field_title = field_config["title"]
            field_type = field_config["type"]
            field_value = field_config["value"]

            print(f"\n🎯 测试字段: {field_title} ({field_type}) = {field_value}")

            # 创建对应类型的组件
            field_component = ComponentFactory.create_component(field_type, tab)
            if not field_component:
                print(f"❌ 无法创建组件类型: {field_type}")
                continue

            # 填充数据
            result = field_component.fill_data(field_title, {
                "value": field_value,
                "require": field_config["require"]
            })

            if result.success:
                print(f"✅ {field_title} 成功: {result.message}")
            else:
                print(f"❌ {field_title} 失败: {result.message}")

            # 等待一段时间以便观察结果
            tab.wait(2)

        print(f"\n🎉 所有字段测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False


def test_find_element():
    """测试查找各种字段元素"""

    print("🎯 测试查找各种字段元素")
    print("=" * 50)

    try:
        # 1. 登录并获取页面
        print("📱 正在登录...")
        temu = Temu("1")
        if not temu.login():
            print("❌ 登录失败")
            return False

        page = temu.page
        print("✅ 登录成功")

        # 2. 导航到商品编辑页面
        print("🔗 正在导航到商品编辑页面...")
        edit_url = "https://seller.kuajingmaihuo.com/goods/product-create/product-edit?productDraftId=8817230645"
        tab = page.new_tab(edit_url)
        tab.wait(5)
        print("✅ 页面加载完成")

        # 3. 先填充ProductSKC数据
        if not fill_product_skc_data(tab):
            print("❌ ProductSKC数据填充失败")
            return False


        # 5. 测试查找各种元素
        test_elements = [
            ("敏感属性", "dropdown_select"),
            ("最长边", "input_number"),
            ("次长边", "input_number"),
            ("最短边", "input_number"),
            ("重量", "input_number")
        ]

        print("\n🔍 测试查找各种字段元素...")
        for field_title, component_type in test_elements:
            print(f"\n🎯 查找字段: {field_title} ({component_type})")

            # 创建对应类型的组件
            field_component = ComponentFactory.create_component(component_type, tab)
            if not field_component:
                print(f"❌ 无法创建组件类型: {component_type}")
                continue

            # 查找元素
            element = field_component.find_field_container(field_title)

            if element:
                print(f"✅ 成功找到 {field_title} 元素")
                print(f"📋 元素标签: {element.tag}")
                print(f"📋 元素属性: {element.attrs}")
            else:
                print(f"❌ 未找到 {field_title} 元素")

            tab.wait(1)

        print(f"\n🎉 所有元素查找测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("测试异常")
        return False


if __name__ == "__main__":
    print("🚀 开始测试敏感属性和尺寸重量字段...")

    # # 首先测试元素查找
    # print("\n=== 测试元素查找 ===")
    # test_find_element()

    # 然后测试完整功能
    print("\n=== 测试完整功能 ===")
    test_sensitive_attribute_select()

    print("\n🎉 所有测试完成！")
